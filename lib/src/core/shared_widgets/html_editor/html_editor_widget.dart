import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';

import '../../resources/app_radius.dart';

class HtmlEditorWidget extends HookWidget {
  final String? title;
  final String? hint;
  final TextEditingController controller;
  final bool isRequired;
  final Widget? icon;

  const HtmlEditorWidget({
    super.key,
    this.title,
    this.hint,
    required this.controller,
    this.isRequired = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final htmlController = HtmlEditorController();
    final showHtmlEditor = useState(false);
    final hasHtmlContent = useState(false);

    useEffect(() {
      // Initialize HTML editor with existing content
      if (controller.text.isNotEmpty) {
        htmlController.setText(controller.text);
        // Check if content contains HTML tags
        hasHtmlContent.value = _containsHtmlTags(controller.text);
      }
      return null;
    }, []);

    // Listen to controller changes to update HTML status
    useEffect(() {
      void listener() {
        hasHtmlContent.value = _containsHtmlTags(controller.text);
      }

      controller.addListener(listener);
      return () => controller.removeListener(listener);
    }, [controller]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Row(
            children: [
              if (icon != null) ...[
                icon!,
                context.smallGap,
              ],
              Expanded(
                child: Text(
                  title!,
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (isRequired)
                Text(
                  ' *',
                  style: context.subTitle.copyWith(
                    color: ColorManager.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          context.smallGap,
        ],

        // Show HTML preview if content has HTML, otherwise show editable text field
        if (hasHtmlContent.value) ...[
          // HTML Preview (read-only)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            decoration: BoxDecoration(
              border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
              color: context.appTheme.cardColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      context.tr.customized_description,
                      style: context.labelMedium.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: Icon(
                        Icons.edit,
                        color: ColorManager.primaryColor,
                        size: 20,
                      ),
                      onPressed: () {
                        _showHtmlEditorBottomSheet(
                          context,
                          htmlController,
                          controller,
                          showHtmlEditor,
                        );
                      },
                    ),
                  ],
                ),
                context.smallGap,
                Html(
                  data: controller.text,
                  style: {
                    "body": Style(
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                      fontSize: FontSize(14),
                      color: context.appTheme.textTheme.bodyLarge?.color,
                    ),
                    "p": Style(
                      margin: Margins.only(bottom: 8),
                    ),
                    "b": Style(
                      fontWeight: FontWeight.bold,
                    ),
                    "br": Style(
                      margin: Margins.only(bottom: 4),
                    ),
                  },
                ),
              ],
            ),
          ),
        ] else ...[
          // Regular text field
          BaseTextField(
            controller: controller,
            hint: hint,
            maxLines: 4,
            radius: AppRadius.imageContainerRadius,
            suffixIcon: IconButton(
              icon: Icon(
                Icons.code,
                color: ColorManager.primaryColor,
              ),
              onPressed: () {
                _showHtmlEditorBottomSheet(
                  context,
                  htmlController,
                  controller,
                  showHtmlEditor,
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  void _showHtmlEditorBottomSheet(
    BuildContext context,
    HtmlEditorController htmlController,
    TextEditingController textController,
    ValueNotifier<bool> showHtmlEditor,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: context.appTheme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: context.appTheme.cardColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    context.tr.rich_text_editor,
                    style: context.title.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: ColorManager.grey,
                    ),
                    child: Text(context.tr.cancel),
                  ),
                  context.smallGap,
                  Button(
                    label: context.tr.save,
                    onPressed: () async {
                      final htmlText = await htmlController.getText();
                      textController.text = htmlText;
                      Navigator.pop(context);
                    },
                  ).sized(height: 40, width: 80),
                ],
              ),
            ),

            // HTML Editor
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: HtmlEditor(
                  controller: htmlController,
                  htmlEditorOptions: HtmlEditorOptions(
                    hint: hint ?? context.tr.enter_your_content_here,
                    initialText: textController.text,
                    darkMode: context.isDark,
                  ),
                  htmlToolbarOptions: HtmlToolbarOptions(
                    defaultToolbarButtons: [
                      const StyleButtons(
                        style: true,
                      ),
                      const FontButtons(
                        bold: true,
                        italic: true,
                        underline: true,
                        clearAll: false,
                        strikethrough: false,
                        superscript: false,
                        subscript: false,
                      ),
                      const ParagraphButtons(
                        textDirection: false,
                        lineHeight: false,
                        caseConverter: false,
                        alignLeft: true,
                        alignCenter: true,
                        alignRight: true,
                        alignJustify: false,
                        increaseIndent: false,
                        decreaseIndent: false,
                      ),
                      const ListButtons(
                        ul: true,
                        ol: true,
                        listStyles: false,
                      ),
                      const ColorButtons(
                        foregroundColor: true,
                        highlightColor: false,
                      ),
                      const InsertButtons(
                        link: true,
                        picture: false,
                        video: false,
                        audio: false,
                        table: false,
                        hr: true,
                        otherFile: false,
                      ),
                      const OtherButtons(
                        fullscreen: false,
                        copy: false,
                        paste: false,
                        codeview: true,
                      ),
                    ],
                    toolbarPosition: ToolbarPosition.aboveEditor,
                    toolbarType: ToolbarType.nativeScrollable,
                    buttonColor: ColorManager.primaryColor,
                    buttonSelectedColor: ColorManager.primaryColor,
                    buttonBorderColor: ColorManager.grey.withOpacity(0.3),
                    buttonBorderWidth: 1,
                    buttonBorderRadius: BorderRadius.circular(8),
                    dropdownBackgroundColor: context.appTheme.cardColor,
                  ),
                  otherOptions: OtherOptions(
                    height: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: ColorManager.grey.withOpacity(0.3),
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to detect HTML content
  bool _containsHtmlTags(String text) {
    if (text.isEmpty) return false;

    // Check for common HTML tags
    final htmlTagPattern = RegExp(r'<[^>]+>', caseSensitive: false);
    return htmlTagPattern.hasMatch(text);
  }
}
