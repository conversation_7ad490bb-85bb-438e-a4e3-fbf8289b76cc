import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';

class HtmlEditorWidget extends HookWidget {
  final String? title;
  final String? hint;
  final TextEditingController controller;
  final bool isRequired;
  final Widget? icon;

  const HtmlEditorWidget({
    super.key,
    this.title,
    this.hint,
    required this.controller,
    this.isRequired = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final htmlController = HtmlEditorController();
    final showHtmlEditor = useState(false);
    final isPreviewMode = useState(false);

    useEffect(() {
      // Initialize HTML editor with existing content
      if (controller.text.isNotEmpty) {
        htmlController.setText(controller.text);
      }
      return null;
    }, []);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Row(
            children: [
              if (icon != null) ...[
                icon!,
                context.smallGap,
              ],
              Text(
                title!,
                style: context.subTitle.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (isRequired)
                Text(
                  ' *',
                  style: context.subTitle.copyWith(
                    color: ColorManager.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          context.smallGap,
        ],

        // Text field with HTML editor button
        BaseTextField(
          controller: controller,
          hint: hint,
          maxLines: 4,
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Preview toggle button
              if (controller.text.isNotEmpty)
                IconButton(
                  icon: Icon(
                    isPreviewMode.value ? Icons.edit : Icons.preview,
                    color: ColorManager.primaryColor,
                  ),
                  onPressed: () {
                    isPreviewMode.value = !isPreviewMode.value;
                  },
                ),
              // HTML editor button
              IconButton(
                icon: Icon(
                  Icons.code,
                  color: ColorManager.primaryColor,
                ),
                onPressed: () {
                  showHtmlEditor.value = true;
                  _showHtmlEditorBottomSheet(
                    context,
                    htmlController,
                    controller,
                    showHtmlEditor,
                  );
                },
              ),
            ],
          ),
        ),

        // HTML Preview
        if (isPreviewMode.value && controller.text.isNotEmpty) ...[
          context.smallGap,
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            decoration: BoxDecoration(
              border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
              color: context.appTheme.cardColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${context.tr.preview}:',
                  style: context.labelMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ColorManager.primaryColor,
                  ),
                ),
                context.smallGap,
                Html(
                  data: controller.text,
                  style: {
                    "body": Style(
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                      fontSize: FontSize(14),
                      color: context.appTheme.textTheme.bodyLarge?.color,
                    ),
                    "p": Style(
                      margin: Margins.only(bottom: 8),
                    ),
                    "b": Style(
                      fontWeight: FontWeight.bold,
                    ),
                    "br": Style(
                      margin: Margins.only(bottom: 4),
                    ),
                  },
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  void _showHtmlEditorBottomSheet(
    BuildContext context,
    HtmlEditorController htmlController,
    TextEditingController textController,
    ValueNotifier<bool> showHtmlEditor,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: context.appTheme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: context.appTheme.cardColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    context.tr.rich_text_editor,
                    style: context.title.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(context.tr.cancel),
                  ),
                  context.smallGap,
                  Button(
                    label: context.tr.save,
                    onPressed: () async {
                      final htmlText = await htmlController.getText();
                      textController.text = htmlText;
                      Navigator.pop(context);
                    },
                  ).sized(height: 40, width: 80),
                ],
              ),
            ),

            // HTML Editor
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: HtmlEditor(
                  controller: htmlController,
                  htmlEditorOptions: HtmlEditorOptions(
                    hint: hint ?? context.tr.enter_your_content_here,
                    initialText: textController.text,
                    darkMode: context.isDark,
                  ),
                  htmlToolbarOptions: HtmlToolbarOptions(
                    defaultToolbarButtons: [
                      const StyleButtons(),
                      const FontSettingButtons(),
                      const FontButtons(),
                      const ColorButtons(),
                      const ListButtons(),
                      const ParagraphButtons(),
                      const InsertButtons(
                        video: false,
                        audio: false,
                        table: false,
                        hr: true,
                        otherFile: false,
                      ),
                      const OtherButtons(
                        copy: false,
                        paste: false,
                        codeview: true,
                      ),
                    ],
                    toolbarPosition: ToolbarPosition.aboveEditor,
                    toolbarType: ToolbarType.nativeScrollable,
                  ),
                  otherOptions: OtherOptions(
                    height: 400,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: ColorManager.grey.withOpacity(0.3),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateButton(
    BuildContext context,
    String label,
    String template,
    HtmlEditorController htmlController,
  ) {
    return ElevatedButton(
      onPressed: () {
        htmlController.insertText(template);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorManager.primaryColor.withOpacity(0.1),
        foregroundColor: ColorManager.primaryColor,
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
      ),
      child: Text(
        label,
        style: context.labelSmall,
      ),
    );
  }

  String _getVegetablesTemplate() {
    return '''
<p><b>Vegetables</b></p>
<p>
1- 2 kg red onions or white onions (write in notes)<br>
2- 2 kg potatoes<br>
3- 3 kg tomatoes<br>
4- 1 kg cucumbers<br>
5- 1 local lettuce<br>
6- 1 kg zucchini or 1 kg sweet potatoes (write in notes)<br>
7- 1 cabbage<br>
8- 500 g eggplant (small variety)<br>
9- 1 kg carrots<br>
10- 1 kg bell peppers (mixed colors)<br>
11- 1 bunch green onions<br>
12- 0.5 kg lemons
</p>
''';
  }

  String _getFruitsTemplate() {
    return '''
<p><b>Fruits</b></p>
<p>
1- 2 kg oranges<br>
2- 1 kg tangerines or 1 kg bananas (write in notes)<br>
3- 1 cantaloupe<br>
4- 1 kg strawberries
</p>
''';
  }

  String _getProductFeaturesTemplate() {
    return '''
<p><b>Product Features</b></p>
<p>
• High quality materials<br>
• Durable construction<br>
• Easy to use<br>
• Excellent value for money<br>
• Customer satisfaction guaranteed
</p>
''';
  }
}
