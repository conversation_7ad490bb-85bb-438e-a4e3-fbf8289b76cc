import 'package:flutter/material.dart'
    show BuildContext, Locale, LocalizationsDelegate;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:idea2app_vendor_app/generated/l10n.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';

class AppConsts {
  static const String appName = 'Idea2App Vendor';
  static const int vendorId = 1;
  static const String smartlookKey = '372e3255eab94176d0d56611d78f2c7c964fe780';
  static const String appFont = 'ProductSans';
  static const String placeholderImage =
      'https://play-lh.googleusercontent.com/rOeG921S-BkkBM3K1fzulZ6czHbPboVY0GQirkmz9aotQZdEMm2HU-tOV_kh-3wRLYl9';
  static const String serverKey =
      'AAAAJg2fz0c:APA91bFw4I29Ml7Iit5T9u_UKIqTvHsM27yRB2WvduXlpLyWrNEDl4ovRzKa1swwscVNyQgezqUgFKPcEZTiyTCW83Sy9MBjQY-0d7IdUO7ZWula-MynT-hwx78i83ZfpMtOhEb8pU73';
  static const String geminiApiKey = 'AIzaSyBAheu9a-0TRBiEAGQN50_QoX0SjWaGu1M';

  static const String oneSignalAppId = '************************************';
  static const String onSignalApiKey =
      'ZmU0OWM3YTMtZGQ3NS00NjA4LTllZjMtOGY2ODA5Yzk0MTVh';
  static const Locale locale = Locale('en');
  static const int animatedDuration = 50;
  static const int orderAnimatedDuration = 5;

  static const int slideAnimatedDuration = 1200;
  static const int errorDuration = 1500;
  static const whiteColor = '0xFFffffff';
  static const blackColor = '0xFF000000';

  static const int maxCatLimit = 2;
  static const int maxProductsLimit = 3;
  static const String defaultCurrencyDocId =
      "oo9nayo9ho4xifog8s5ed24v"; // ? L.E
  static const String defaultCountryDocId =
      "toslusa8ic8lxl4sjv0stik7"; // ? Egypt

  static String vendorWebsiteLink() {
    final currentVendor = VendorModelHelper.currentVendor();

    final link = 'https://i2shop.store/${currentVendor['business_name']}';

    return link;
  }

  static const List<Locale> supportedLocales = [locale, Locale('ar')];

  static const String vodafoneCash = 'Vodafone Cash';
  static const String etisalatCash = 'Etisalat Cash';
  static const String orangeCash = 'Orange Cash';
  static const String instapay = 'InstaPay';
  static const String bankAccount = 'Bank Account';
  static const String fawaterak = 'Online Payment';
  static const String cod = 'COD';
  static const String paypal = 'Paypal';

  static const String adminTopic = 'default';
  static const String payTabs = 'PayTabs';

  //
  static const List<String> paymentMethods = [
    cod,
    fawaterak,
    paypal,
    payTabs,
    vodafoneCash,
    etisalatCash,
    orangeCash,
    instapay,
    bankAccount,
  ];

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  // Raw area data for reference
  static final Map<String, List<Map<String, String>>> _rawAreasData = {
    "cairo": [
      {"nameEn": "15 May", "nameAr": "15 مايو"},
      {"nameEn": "Al Azbakeyah", "nameAr": "الازبكية"},
      {"nameEn": "Al Basatin", "nameAr": "البساتين"},
      {"nameEn": "Tebin", "nameAr": "التبين"},
      {"nameEn": "El-Khalifa", "nameAr": "الخليفة"},
      {"nameEn": "El darrasa", "nameAr": "الدراسة"},
      {"nameEn": "Aldarb Alahmar", "nameAr": "الدرب الاحمر"},
      {"nameEn": "Zawya al-Hamra", "nameAr": "الزاوية الحمراء"},
      {"nameEn": "El-Zaytoun", "nameAr": "الزيتون"},
      {"nameEn": "Sahel", "nameAr": "الساحل"},
      {"nameEn": "El Salam", "nameAr": "السلام"},
      {"nameEn": "Sayeda Zeinab", "nameAr": "السيدة زينب"},
      {"nameEn": "El Sharabeya", "nameAr": "الشرابية"},
      {"nameEn": "Shorouk", "nameAr": "مدينة الشروق"},
      {"nameEn": "El Daher", "nameAr": "الظاهر"},
      {"nameEn": "Ataba", "nameAr": "العتبة"},
      {"nameEn": "New Cairo", "nameAr": "القاهرة الجديدة"},
      {"nameEn": "El Marg", "nameAr": "المرج"},
      {"nameEn": "Ezbet el Nakhl", "nameAr": "عزبة النخل"},
      {"nameEn": "Matareya", "nameAr": "المطرية"},
      {"nameEn": "Maadi", "nameAr": "المعادى"},
      {"nameEn": "Maasara", "nameAr": "المعصرة"},
      {"nameEn": "Mokattam", "nameAr": "المقطم"},
      {"nameEn": "Manyal", "nameAr": "المنيل"},
      {"nameEn": "Mosky", "nameAr": "الموسكى"},
      {"nameEn": "Nozha", "nameAr": "النزهة"},
      {"nameEn": "Waily", "nameAr": "الوايلى"},
      {"nameEn": "Bab al-Shereia", "nameAr": "باب الشعرية"},
      {"nameEn": "Bolaq", "nameAr": "بولاق"},
      {"nameEn": "Garden City", "nameAr": "جاردن سيتى"},
      {"nameEn": "Hadayek El-Kobba", "nameAr": "حدائق القبة"},
      {"nameEn": "Helwan", "nameAr": "حلوان"},
      {"nameEn": "Dar Al Salam", "nameAr": "دار السلام"},
      {"nameEn": "Shubra", "nameAr": "شبرا"},
      {"nameEn": "Tura", "nameAr": "طره"},
      {"nameEn": "Abdeen", "nameAr": "عابدين"},
      {"nameEn": "Abaseya", "nameAr": "عباسية"},
      {"nameEn": "Ain Shams", "nameAr": "عين شمس"},
      {"nameEn": "Nasr City", "nameAr": "مدينة نصر"},
      {"nameEn": "New Heliopolis", "nameAr": "مصر الجديدة"},
      {"nameEn": "Masr Al Qadima", "nameAr": "مصر القديمة"},
      {"nameEn": "Mansheya Nasir", "nameAr": "منشية ناصر"},
      {"nameEn": "Badr City", "nameAr": "مدينة بدر"},
      {"nameEn": "Obour City", "nameAr": "مدينة العبور"},
      {"nameEn": "Cairo Downtown", "nameAr": "وسط البلد"},
      {"nameEn": "Zamalek", "nameAr": "الزمالك"},
      {"nameEn": "Kasr El Nile", "nameAr": "قصر النيل"},
      {"nameEn": "Rehab", "nameAr": "الرحاب"},
      {"nameEn": "Katameya", "nameAr": "القطامية"},
      {"nameEn": "Madinty", "nameAr": "مدينتي"},
      {"nameEn": "Rod Alfarag", "nameAr": "روض الفرج"},
      {"nameEn": "Sheraton", "nameAr": "شيراتون"},
      {"nameEn": "El-Gamaleya", "nameAr": "الجمالية"},
      {"nameEn": "10th of Ramadan City", "nameAr": "العاشر من رمضان"},
      {"nameEn": "Helmeyat Alzaytoun", "nameAr": "الحلمية"},
      {"nameEn": "New Nozha", "nameAr": "النزهة الجديدة"},
      {"nameEn": "Capital New", "nameAr": "العاصمة الإدارية"},
    ],
    "giza": [
      {"nameEn": "Sixth of October", "nameAr": "السادس من أكتوبر"},
      {"nameEn": "Cheikh Zayed", "nameAr": "الشيخ زايد"},
      {"nameEn": "Hawamdiyah", "nameAr": "الحوامدية"},
      {"nameEn": "Al Badrasheen", "nameAr": "البدرشين"},
      {"nameEn": "Saf", "nameAr": "الصف"},
      {"nameEn": "Atfih", "nameAr": "أطفيح"},
      {"nameEn": "Al Ayat", "nameAr": "العياط"},
      {"nameEn": "Al-Bawaiti", "nameAr": "الباويطي"},
      {"nameEn": "ManshiyetAl Qanater", "nameAr": "منشأة القناطر"},
      {"nameEn": "Oaseem", "nameAr": "أوسيم"},
      {"nameEn": "Kerdasa", "nameAr": "كرداسة"},
      {"nameEn": "Abu Nomros", "nameAr": "أبو النمرس"},
      {"nameEn": "Kafr Ghati", "nameAr": "كفر غطاطي"},
      {"nameEn": "Manshiyet Al Bakari", "nameAr": "منشأة البكاري"},
      {"nameEn": "Dokki", "nameAr": "الدقى"},
      {"nameEn": "Agouza", "nameAr": "العجوزة"},
      {"nameEn": "Haram", "nameAr": "الهرم"},
      {"nameEn": "Warraq", "nameAr": "الوراق"},
      {"nameEn": "Imbaba", "nameAr": "امبابة"},
      {"nameEn": "Boulaq Dakrour", "nameAr": "بولاق الدكرور"},
      {"nameEn": "Al Wahat Al Baharia", "nameAr": "الواحات البحرية"},
      {"nameEn": "Omraneya", "nameAr": "العمرانية"},
      {"nameEn": "Moneeb", "nameAr": "المنيب"},
      {"nameEn": "Bin Alsarayat", "nameAr": "بين السرايات"},
      {"nameEn": "Kit Kat", "nameAr": "الكيت كات"},
      {"nameEn": "Mohandessin", "nameAr": "المهندسين"},
      {"nameEn": "Faisal", "nameAr": "فيصل"},
      {"nameEn": "Abu Rawash", "nameAr": "أبو رواش"},
      {"nameEn": "Hadayek Alahram", "nameAr": "حدائق الأهرام"},
      {"nameEn": "Haraneya", "nameAr": "الحرانية"},
      {"nameEn": "Hadayek October", "nameAr": "حدائق اكتوبر"},
      {"nameEn": "Saft Allaban", "nameAr": "صفط اللبن"},
      {"nameEn": "Smart Village", "nameAr": "القرية الذكية"},
      {"nameEn": "Ard Ellwaa", "nameAr": "ارض اللواء"},
    ],
  };

  // Convert raw data to AreaModel objects
  static Map<String, List<AreaModel>> get areas {
    final Map<String, List<AreaModel>> result = {};

    _rawAreasData.forEach((cityName, areasList) {
      result[cityName] = areasList
          .map(
            (areaData) => AreaModel(
                nameEn: areaData['nameEn'],
                nameAr: areaData['nameAr'],
                isFreeShipping: false,
                cost: 0,
                isActive: true),
          )
          .toList();
    });

    return result;
  }

  static List<AreaModel?> getAreasByCityName(
    String cityName,
    BuildContext context,
  ) {
    final areasWithCityName = areas[cityName.toLowerCase()] ?? [];

    return areasWithCityName;
  }
}
