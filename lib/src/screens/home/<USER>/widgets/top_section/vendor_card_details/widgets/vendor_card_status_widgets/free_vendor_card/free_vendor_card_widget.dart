import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view_model/banner_VM.dart';
import 'package:provider/provider.dart';
import 'package:zo_animated_border/zo_animated_border.dart';

import '../../../../../../../../../core/consts/app_constants.dart';
import '../../../../../../../../subscription_plans/view/vendor_choose_plan_screen.dart';
import '../active_vendor_card/vendor_website_widget.dart';

class FreeVendorCardWidget extends HookWidget {
  final bool canViewLinkActions;

  const FreeVendorCardWidget({super.key, required this.canViewLinkActions});

  @override
  Widget build(BuildContext context) {
    // final bannerVM = context.read<BannerVM>();
    //
    // useEffect(() {
    //   WidgetsBinding.instance.addPostFrameCallback((_) async {
    //   });
    //   return () {};
    // }, []);

    final websiteLink = AppConsts.vendorWebsiteLink();
    return Column(
      children: [
        Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSpaces.smallPadding),
                decoration: BoxDecoration(
                  color: Color(0x5ed9d9d9),
                  borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    VendorWebsiteLink(
                      websiteLink: websiteLink,
                    ),
                    Icon(Icons.arrow_forward_ios_rounded,
                        color: Colors.white, size: 15)
                  ],
                ),
              ),
              context.mediumGap,
              InkWell(
                onTap: () => context.to(const VendorChoosePlanScreen()),
                child: ZoSnakeBorder(
                  duration: 5,
                  glowOpacity: .2,
                  snakeHeadColor: Colors.white,
                  snakeTailColor: Colors.white.withOpacity(.4),
                  snakeTrackColor: Colors.transparent,
                  borderWidth: 2,
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.all(AppSpaces.smallPadding),
                    decoration: BoxDecoration(
                      color: Color(0x5ed9d9d9),
                      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                    ),
                    child: Text(
                      context.tr.subscribeNow,
                      style: context.whiteLabelLarge,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
