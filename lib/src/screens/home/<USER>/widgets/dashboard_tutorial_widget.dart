import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/vendor_videos_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view_model/settings_view_model.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart' hide AppSpaces;

class DashboardTutorialWidget extends StatelessWidget {
  const DashboardTutorialWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsVM = context.watch<SettingsVM>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
          child: Text(context.tr.dashboardTutorial, style: context.title),
        ),
        context.smallGap,
        ListView.separated(
          padding: EdgeInsets.all(AppSpaces.largePadding),
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final video = settingsVM.settings?.vendorVideos[index];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {
                    openURL(video?.url ?? 'https://www.youtube.com/@idea2app');
                  },
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      BaseCachedImage(
                        video?.image.url ?? '',
                        fit: BoxFit.cover,
                        radius: AppRadius.radius16,
                      ),
                      CircleAvatar(
                        radius: 24.r,
                        backgroundColor: ColorManager.black.withOpacity(0.5),
                        child: Icon(
                          Icons.play_arrow,
                          color: ColorManager.white,
                          size: 32.r,
                        ),
                      )
                    ],
                  ),
                ),
                context.mediumGap,
                Text(video?.toLocalName(context) ?? '',
                    style: context.subHeadLine.copyWith()),
                context.smallGap,
                Text(
                  video?.toLocalDescription(context) ?? '',
                  style: context.hint,
                )
              ],
            );
          },
          separatorBuilder: (context, index) => Column(
            children: [
              context.smallGap,
              Divider(color: ColorManager.grey.withOpacity(0.7)),
              context.smallGap,
            ],
          ),
          itemCount: settingsVM.settings?.vendorVideos.length ?? 0,
        )
      ],
    );
  }
}
