import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';

extension VendorVideosModelExtension on VendorVideosModel {
  String toLocalName(BuildContext context) {
    if (context.isEng) {
      return name.isEmpty ? nameAr : name;
    } else {
      return nameAr.isEmpty ? name : nameAr;
    }
  }

  String toLocalDescription(BuildContext context) {
    if (context.isEng) {
      return description.isEmpty ? descriptionAr : description;
    } else {
      return descriptionAr.isEmpty ? description : descriptionAr;
    }
  }
}

class VendorVideosModel {
  final String name;
  final String nameAr;
  final String description;
  final String descriptionAr;
  final String url;
  final BaseMediaModel image;

  VendorVideosModel({
    this.name = '',
    this.nameAr = '',
    this.description = '',
    this.descriptionAr = '',
    this.url = '',
    required this.image,
  });

  factory VendorVideosModel.fromJson(Map<String, dynamic> json) {
    return VendorVideosModel(
      name: json[ApiStrings.title] ?? '',
      nameAr: json[ApiStrings.arabicTitle] ?? '',
      description: json[ApiStrings.description] ?? '',
      descriptionAr: json[ApiStrings.arabicDescription] ?? '',
      url: json[ApiStrings.url] ?? '',
      image: BaseMediaModel.fromJson(json[ApiStrings.image] ?? {}),
    );
  }
}
