import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/animation_extensions.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/widgets/templates_drop_down.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/utils/logger.dart';

class RegisterFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<TemplateModel?> selectedBusinessType;

  const RegisterFields(
      {super.key,
      required this.controllers,
      required this.selectedBusinessType});

  @override
  Widget build(BuildContext context) {
    var isObscure = useState<bool>(true);
    final websiteName =
        useState<String?>(controllers[ApiStrings.websiteName]?.text);
    final isEditingWebsite = useState<bool>(false);

    return Consumer2<AuthVM, MediaVM>(
      builder: (context, authVM, mediaVM, child) {
        return Column(
          children: [
            //! Image
            SinglePickImageWidget(
              title: context.tr.clickToBrowseLogo,
              pickedResult: mediaVM.filesPaths.firstOrNull,
            ).topSlide,

            context.largeGap,

            //! Name Field
            AuthTextField(
              controller: controllers[ApiStrings.name],
              icon: const Icon(
                Icons.drive_file_rename_outline,
              ),
              onChanged: (value) {
                final formattedValue = value
                    .replaceAll(' ', '-')
                    .replaceAll(RegExp(r'[^a-zA-Z0-9-_]'), '');

                final websiteValue = formattedValue.toLowerCase();
                controllers[ApiStrings.websiteName]?.text = websiteValue;
                websiteName.value = websiteValue;

                Log.w('Business Name Changed: $websiteValue');
              },
              title: context.tr.businessName,
              textInputType: TextInputType.name,
              hint: context.tr.businessName,
            ).rightSlide,

            context.mediumGap,
            if (websiteName.value != null && websiteName.value!.isNotEmpty) ...[
              context.smallGap,
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                decoration: BoxDecoration(
                    color: context.appTheme.cardColor,
                    borderRadius:
                        BorderRadius.circular(AppRadius.baseContainerRadius),
                    boxShadow: context.isDark
                        ? ConstantsWidgets.darkBoxShadowFromBottom
                        : ConstantsWidgets.boxShadowFromBottom),
                child: Column(
                  children: [
                    if (isEditingWebsite.value) ...[
                      AuthTextField(
                        controller: controllers[ApiStrings.websiteName],
                        icon: const Icon(Icons.link),
                        title: context.tr.websiteName,
                        textInputType: TextInputType.name,
                        hint: context.tr.websiteName,
                        onChanged: (value) {
                          if (value.isEmpty) {
                            websiteName.value = '';
                            return;
                          }

                          final formattedValue = value
                              .replaceAll(' ', '-')
                              .replaceAll(RegExp(r'[^a-zA-Z0-9-_]'), '');

                          websiteName.value = formattedValue.toLowerCase();
                        },
                      ),
                      context.mediumGap,
                    ],
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          context.tr.freeWebsite,
                          style: context.title,
                        ),
                        context.smallGap,
                        GestureDetector(
                          onTap: () {
                            if (!isEditingWebsite.value) {
                              controllers[ApiStrings.websiteName]?.text =
                                  websiteName.value ?? '';

                              log('WebsitefsfsName: ${controllers[ApiStrings.websiteName]?.text} ffff ${websiteName.value}');
                            }

                            isEditingWebsite.value = !isEditingWebsite.value;
                          },
                          child: CircleAvatar(
                            backgroundColor: ColorManager.primaryColor,
                            maxRadius: 15,
                            child: Icon(
                              isEditingWebsite.value ? Icons.check : Icons.edit,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                    context.xSmallGap,
                    Text('https://i2shop.store/${websiteName.value ?? ''}',
                        textAlign: TextAlign.center,
                        style: context.labelLarge.copyWith(
                            color: ColorManager.primaryColor,
                            fontWeight: FontWeight.bold)),
                    context.smallGap,
                    Divider(
                      color: context.isDark
                          ? ColorManager.grey.withOpacity(0.5)
                          : ColorManager.grey,
                      thickness: 1,
                    ),
                    context.smallGap,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(context.tr.premiumWebsite, style: context.title),
                        context.smallGap,
                        Text(context.tr.noDomainFees, style: context.hint),
                      ],
                    ),
                    context.xSmallGap,
                    Text('https://${websiteName.value ?? ''}.i2shop.store',
                        textAlign: TextAlign.center,
                        style: context.labelLarge.copyWith(
                            color: ColorManager.primaryColor,
                            fontWeight: FontWeight.bold)),
                    context.smallGap,
                    Text(
                      context.tr.changeWebsiteLater,
                      style: context.hint,
                    ),
                  ],
                ),
              ),
              context.largeGap,
            ],

            TemplatesDropDown(
              selectedBusinessType: selectedBusinessType,
            ).leftSlide,

            context.mediumGap,

            //! Email Field
            AuthTextField(
              controller: controllers[ApiStrings.email],
              icon: const Icon(
                Icons.alternate_email,
              ),
              title: context.tr.email,
              hint: context.tr.email,
              textInputType: TextInputType.emailAddress,
              validator: (value) {
                if (value!.isEmpty) {
                  return context.tr.requiredField;
                }

                final regex =
                    RegExp(r'^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$');

                if (!regex.hasMatch(value)) {
                  return context.tr.emailIsInvalid;
                }

                return null;
              },
            ).rightSlide,

            context.mediumGap,

            //! Password Field
            AuthTextField(
              controller: controllers[ApiStrings.password],
              isObscure: isObscure.value ? true : false,
              textInputType: TextInputType.visiblePassword,
              title: context.tr.password,
              hint: context.tr.password,
              icon: const Icon(
                Icons.lock_outline,
              ),
              suffixIcon: IconButton(
                onPressed: () {
                  isObscure.value = !isObscure.value;
                },
                icon: Icon(
                  isObscure.value
                      ? Icons.remove_red_eye
                      : Icons.visibility_off_rounded,
                ),
              ),
            ).leftSlide,

            context.mediumGap,

            //! Phone Field
            AuthTextField(
              controller: controllers[ApiStrings.phone],
              icon: const Icon(
                Icons.phone,
              ),
              title: context.tr.phone,
              hint: context.tr.phone,
              textInputType: TextInputType.phone,
              validator: (value) {
                if (value!.isEmpty) {
                  return context.tr.requiredField;
                }

                if (value.length < 10) {
                  return context.tr.phoneIsInvalid;
                }

                return null;
              },
              isRequired: false,
            ).leftSlide,
          ],
        );
      },
    );
  }
}
