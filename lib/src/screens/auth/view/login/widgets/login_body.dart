import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/animation_extensions.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/widgets/login_fields.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/register.screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../generated/assets.dart';
import '../../../../banner/view_model/banner_VM.dart';

class LoginBody extends HookWidget {
  const LoginBody({super.key});

  @override
  Widget build(BuildContext context) {
    final emailController = useTextEditingController(
      // text: kDebugMode ? '<EMAIL>' : '',
      text: kDebugMode ? '<EMAIL>' : '',
    );
    final passwordController = useTextEditingController(
      // text: kDebugMode ? '12345678' : '',
      text: kDebugMode ? 'default@123' : '',
    );

    final formKey = useState(GlobalKey<FormState>());

    return Form(
      key: formKey.value,
      child: Stack(
        children: [
          //! Fields Container
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(
                horizontal: AppSpaces.mediumPadding,
              ),
              decoration: BoxDecoration(
                  color: context.isDark
                      ? ColorManager.darkBackgroundColor
                      : ColorManager.backgroundColor,
                  borderRadius: const BorderRadius.all(
                      Radius.circular(AppRadius.baseContainerRadius + 10))),
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: SingleChildScrollView(
                child: Consumer<AuthVM>(
                  builder: (context, authVM, child) {
                    return Column(
                      children: [
                        Image.asset(
                          context.isDark
                              ? Assets.imagesSplashLogo
                              : Assets.imagesSplashLogoBlack,
                          height: 150.h,
                          fit: BoxFit.cover,
                        ).shimmer(
                          color: context.isDark
                              ? ColorManager.accentColor
                              : ColorManager.grey.withOpacity(.5),
                        ),

                        Text(
                          context.tr.letsLoginToYourAccountFirst,
                          style: context.headLine,
                          textAlign: TextAlign.center,
                        ).topSlide,

                        context.xLargeGap,

                        //! Login formField
                        LoginFields(
                          emailController: emailController,
                          passwordController: passwordController,
                        ),

                        context.largeGap,

                        //! Login Button
                        Button(
                          radius: 40,
                          isLoading: authVM.isLoading,
                          onPressed: () async {
                            if (!formKey.value.currentState!.validate()) return;

                            await context.read<AuthVM>().login(context,
                                email: emailController.text.trim(),
                                password: passwordController.text.trim());

                            context
                                .read<BannerVM>()
                                .getVendorBannersData(context);
                          },
                          label: context.tr.login,
                        ).bottomSlide,

                        context.mediumGap,

                        //! Don't have an account
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(context.tr.dontHaveAccount,
                                style: context.labelMedium),
                            InkWell(
                              borderRadius:
                                  BorderRadius.circular(AppRadius.baseRadius),
                              onTap: () {
                                context.to(const RegisterScreen());
                              },
                              child: Text(
                                context.tr.register,
                                style: context.labelMedium.copyWith(
                                  color: ColorManager.primaryColor,
                                  decoration: TextDecoration.underline,
                                  decorationColor: ColorManager.primaryColor,
                                ),
                              ).paddingAll(AppSpaces.xSmallPadding),
                            ).paddingAll(AppSpaces.xSmallPadding - 2),
                          ],
                        ).bottomSlide,

                        context.xLargeGap,
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
