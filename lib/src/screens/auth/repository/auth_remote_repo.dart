import 'dart:async';
import 'dart:developer';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/pricing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/country/model/country_model.dart';
import 'package:idea2app_vendor_app/src/screens/currency/model/currency_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../payment/model/payment_model.dart';
import '../../shipping/model/city_cost_model.dart';
import '../../shipping/model/shipping_model.dart';
import '../models/helper_models/config_model.dart';

enum VendorExistType {
  email,
  businessName,
  emailAndBusinessName,
  none,
}

class AuthRemoteRepository with BaseRepository {
  final BaseApiServices networkApiServices;

  AuthRemoteRepository({
    required this.networkApiServices,
  });

  // * Get Vendors =======================================
  Future<List<VendorModel>> getVendors() async {
    try {
      final response = await networkApiServices.getResponse(
        ApiEndPoints.vendors,
      );

      final vendorsData = (response as List)
          .map((vendor) => VendorModel.fromJson(vendor))
          .toList();

      return vendorsData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  // * Get Current Vendor =======================================
  Future<VendorModel?> getCurrentVendor({
    int populateLevel = 4, // Increased to 4 for sub-vendors
  }) async {
    try {
      final response = await networkApiServices.getResponse(
          '${ApiEndPoints.vendors}/${VendorModelHelper.currentVendorDocumentId()}',
          populateLevel: populateLevel);

      if (response == null) return null;

      final vendorsData = VendorModel.fromJson(response);

      return vendorsData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  // * Check If Vendor Exist =======================================
  Future<VendorExistType> isVendorExistByEmailOrBusinessName({
    required String? email,
    required String? businessName,
  }) async {
    try {
      final String query =
          '?filters[\$or][0][email][\$eq]=$email&filters[\$or][1][business_name][\$eq]=$businessName';

      final response = await networkApiServices.getResponse(
        ApiEndPoints.vendors + query,
      );

      if (response != null && response.isNotEmpty) {
        final vendor = (response as List?)?.firstOrNull;

        final bool emailExists = vendor['email'] == email;
        final bool businessNameExists = vendor['business_name'] == businessName;

        if (emailExists && businessNameExists) {
          return VendorExistType.emailAndBusinessName;
        } else if (emailExists) {
          return VendorExistType.email;
        } else if (businessNameExists) {
          return VendorExistType.businessName;
        }
      }

      return VendorExistType.none;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  // isVendorExistByEmail&password
  Future<VendorModel?> isVendorExistByEmailAndPassword({
    required String? email,
    required String? password,
  }) async {
    try {
      final String query =
          '?filters[email][\$eq]=$email&filters[password][\$eq]=$password';

      final response = await networkApiServices.getResponse(
        ApiEndPoints.vendors + query,
      );

      if (response != null && response.isNotEmpty) {
        final vendor = (response as List?)?.firstOrNull;

        final vendorModel = VendorModel.fromJson(vendor);

        return vendorModel;
      }

      return null;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //? Update Vendor
  Future<void> updateVendorData({
    required VendorModel vendor,
    bool fromLogin = false,
  }) async {
    try {
      NotificationService.subscribeToTopic(vendor.businessName.toString());

      await networkApiServices.putResponse(
        ApiEndPoints.vendors,
        data: fromLogin
            ? {
                ApiStrings.documentId: vendor.documentId,
                ApiStrings.deviceToken: vendor.fcmToken,
                ApiStrings.deviceType: getCurrentDeviceType(),
              }
            : vendor.toJson(),
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //? Edit Vendor
  Future<void> editVendor({
    required VendorModel vendor,
    String? pickedImage,
    String? pricingDocId,
  }) async {
    try {
      await networkApiServices.putResponse(ApiEndPoints.vendors,
          data: vendor.toJson(),
          fileResult: [pickedImage ?? ''],
          fieldName: ApiStrings.logo);

      if (pricingDocId != null) {
        await networkApiServices.connectRelation(data: {
          "pricing": {
            "set": [pricingDocId],
          }
        });
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //? Register Vendor
  Future<VendorModel> registerVendor({
    required VendorModel vendor,
    required int currencyId,
    required int countryId,
    required List<CityCostModel> cities,
    String? pickedImage,
    int? pricingId,
  }) async {
    try {
      final res = await networkApiServices.postResponse(
        ApiEndPoints.vendors,
        data: vendor.toJson(),
        fileResult: [pickedImage ?? ''],
        fieldName: ApiStrings.logo,
        connectWithVendorRelation: false,
      );

      final shippingId =
          await addShipping(vendorName: res['name'], cities: cities);

      final paymentId = await addPayment();

      final configId = await addConfig(
        currencyId: currencyId,
        countryId: countryId,
      );

      final vendorData = VendorModel.fromJson(res);

      log('vendorData ========= ${vendorData.toJson()}');

      final fcmToken = await NotificationService.getToken();

      final copiedVendor = vendorData.copyWith(
        documentId: res['documentId'],
        fcmToken: fcmToken,
        config: ConfigModel(
          id: configId,
        ),
        payments: PaymentModel(
          id: paymentId,
        ),
        shippings: ShippingModel(
          id: shippingId,
        ),
        pricing: pricingId != null
            ? PricingModel(
                id: pricingId,
              )
            : null,
      );

      log('copiedVendor ========= ${copiedVendor.toJson()}');

      // * Update Vendor
      await updateVendorData(
        vendor: copiedVendor,
      );

      final updatedVendor = await getCurrentVendor();

      return updatedVendor ?? vendorData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<int> addConfig(
      {required int currencyId, required int countryId}) async {
    try {
      final config = ConfigModel(
        currencies: [
          CurrencyModel(
            id: currencyId,
          )
        ],
        countries: [
          CountryModel(
            id: countryId,
          )
        ],
      );

      final data = await networkApiServices.postResponse(
        ApiEndPoints.configs,
        connectWithVendorRelation: false,
        data: config.toJson(),
      );

      final configId = data['id'];

      return configId;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<int> addPayment() async {
    try {
      final defaultPayment = PaymentModel(
        title: 'COD',
        description: 'Cash On Delivery',
      );

      final data = await networkApiServices.postResponse(
        ApiEndPoints.payment,
        data: defaultPayment.toJson(),
        connectWithVendorRelation: false,
      );

      final id = data['id'];

      return id;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<int> addShipping({
    required String? vendorName,
    required List<CityCostModel> cities,
  }) async {
    try {
      final shipping = ShippingModel(
        title: '$vendorName - Shipping',
        cost: 0,
      );

      final copiedShipping = shipping.copyWith(citiesCost: cities);

      final data = await networkApiServices.postResponse(
        ApiEndPoints.shipping,
        connectWithVendorRelation: false,
        data: copiedShipping.toJson(),
      );

      final id = data['id'];

      return id;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<void> updateVendorConfig({
    required ConfigModel config,
    bool isCheckoutSettings = false,
  }) async {
    try {
      final data = await networkApiServices.putResponse(
        ApiEndPoints.configs,
        data: isCheckoutSettings
            ? config.toCheckoutSettingsJson()
            : config.toJson(),
      );

      final documentId = data[ApiStrings.documentId] as String?;

      await networkApiServices.connectRelation(data: {
        "config": {
          "set": [documentId],
        }
      });
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
