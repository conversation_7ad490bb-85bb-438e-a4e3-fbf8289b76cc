import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/repository/vendors_repository.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/app.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/repository/auth_local_repo.dart';
import 'package:idea2app_vendor_app/src/screens/auth/repository/auth_remote_repo.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/login.screen.dart';
import 'package:idea2app_vendor_app/src/screens/banner/repository/banner_repository.dart';
import 'package:idea2app_vendor_app/src/screens/categories/repository/category_repository.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

import '../../../core/data/local/shared_preferences/get_storage.dart';
import '../../orders/view_model/order_view_model.dart';
import '../../shipping/model/city_cost_model.dart';
import '../models/helper_models/config_model.dart';

class AuthVM extends BaseVM {
  final AuthRemoteRepository authRepository;
  final AuthLocalRepo authLocalRepo;
  final VendorsRepository vendorsRepository;
  final CategoryRepository categoryRepository;
  final BannerRepository bannerRepository;

  AuthVM({
    required this.vendorsRepository,
    required this.authRepository,
    required this.authLocalRepo,
    required this.categoryRepository,
    required this.bannerRepository,
  });

// * ==================================================================
// !                            Getters
// ? ==================================================================

  VendorModel? _currentVendor;

  VendorModel? get currentVendor => _currentVendor;

  bool isExpiredFromOneWeek(BuildContext context) {
    final expireDate = _currentVendor?.expireDate;
    final now = DateTime.now();

    if (expireDate == null) return false;

    final difference = expireDate.difference(now).inDays;

    final isExpiredFromOneWeek = difference <= -7;

    return isExpiredFromOneWeek;
  }

  //! Get Current Currency ========================================
  bool get isFree {
    final vendorType = _currentVendor?.vendorType;
    return vendorType == VendorType.free;
  }

  //! Get Current Currency ========================================
  bool get isActive {
    return _currentVendor?.isActive ?? true;
  }

  //! Get Current Currency ========================================
  String currentCurrency(BuildContext context) {
    final isEng = context.isEng;

    final currency = _currentVendor?.config?.currencies.firstOrNull;

    final currencyName = isEng ? currency?.currencyEn : currency?.currencyAr;

    return currencyName ?? 'L.E';
  }

  //! Get Current Local Vendor ========================================
  VendorModel? getVendor() {
    _currentVendor = authLocalRepo.getVendorData();

    notifyListeners();

    return _currentVendor;
  }

// * ==================================================================
// !                      Remote Operations
// ? ==================================================================

  // * Get Remote Current Vendor ========================================
  Future<VendorModel?> getCurrentVendor(BuildContext context) async {
    return await baseFunction(context, () async {
      if (VendorModelHelper.currentVendorDocumentId() == null) {
        return null;
      }

      final remoteVendor = await authRepository.getCurrentVendor();
      final localVendor = authLocalRepo.getVendorData();

      if (localVendor != null &&
          localVendor.password!.isNotEmpty &&
          remoteVendor?.password != localVendor.password) {
        Log.e('🚫 Password not match');

        return null;
      } else {
        Log.i('✅ Password match');
      }

      _currentVendor = remoteVendor;
      await authLocalRepo.setVendorData(vendor: _currentVendor!);

      return _currentVendor;
    }, isLoading: false);
  }

  // * Login Vendor ========================================
  Future<bool> login(
    BuildContext context, {
    required String email,
    required String password,
  }) async {
    return await baseFunction(context, () async {
      final vendor = await authRepository.isVendorExistByEmailAndPassword(
          email: email, password: password);

      if (vendor != null && vendor.vendorType != VendorType.deleted) {
        authLocalRepo.setVendorData(vendor: vendor);
        getVendor();

        final fcmToken = await NotificationService.getToken();

        final copiedVendor = _currentVendor!.copyWith(
          fcmToken: fcmToken,
        );

        authRepository.updateVendorData(vendor: copiedVendor, fromLogin: true);

        success();

        final orderVM = context.read<OrderVM>();
        final categoriesVM = context.read<CategoryVM>();

        categoriesVM.clearCategories();
        await orderVM.getHomeOrders(context);
        orderVM.getOrders(context);
        await orderVM.getOrderStatistics(context);

        context.toReplacement(const SelectedScreen());
      } else {
        if (!context.mounted) return false;

        context.showBarMessage(context.tr.wrong_email_or_password,
            isError: true);

        error();

        await Future.delayed(const Duration(seconds: 2));

        reset();
      }

      return vendor;
    }, buttonController: buttonController);
  }

  // * Register Vendor ========================================
  Future<void> registerVendor(
    BuildContext context, {
    required Map<String, TextEditingController> controllers,
    required TemplateModel? selectedBusinessType,
    required String pickedImage,
    required int currencyId,
    required int countryId,
    required List<CityCostModel> cities,
  }) async {
    return await baseFunction(context, () async {
      final vendor = VendorModel(
        name: controllers[ApiStrings.name]!.text,
        email: controllers[ApiStrings.email]!.text.trim(),
        password: controllers[ApiStrings.password]!.text.trim(),
        phone: controllers[ApiStrings.phone]!.text,
        vendorType: VendorType.free,
        startDate: DateTime.now().toLocal(),
        businessName: controllers[ApiStrings.websiteName]!.text,
        businessType: selectedBusinessType?.name,
      );

      final isVendorExist =
          await authRepository.isVendorExistByEmailOrBusinessName(
        email: vendor.email,
        businessName: vendor.businessName,
      );

      if (isVendorExist == VendorExistType.email) {
        context.showBarMessage(context.tr.emailAlreadyExist, isError: true);
        return;
      }

      if (isVendorExist == VendorExistType.businessName) {
        context.showBarMessage(context.tr.websiteNameAlreadyExist,
            isError: true);
        return;
      }

      if (isVendorExist == VendorExistType.emailAndBusinessName) {
        context.showBarMessage(context.tr.emailAndWebsiteNameAlreadyExist,
            isError: true);
        return;
      }

      final registeredVendor = await authRepository.registerVendor(
        vendor: vendor,
        pickedImage: pickedImage,
        cities: cities,
        countryId: countryId,
        currencyId: currencyId,
      );

      NotificationService.sendNotification(
        title: 'New Vendor Registered 🎊',
        body: '${registeredVendor.name} is registered successfully ✅',
        userTokenOrTopic: AppConsts.adminTopic, //? Admins
        isTopic: true,
      );

      await authLocalRepo.setVendorData(vendor: registeredVendor);

      if (!context.mounted) return;

      success();

      getVendor();

      final categoriesVM = context.read<CategoryVM>();

      getCurrentVendor(context);
      categoriesVM.clearCategories();
      final orderVM = context.read<OrderVM>();
      orderVM.clearAllOrdersAndFilters();

      context.toReplacement(const SelectedScreen());

      context.read<MediaVM>().clearFiles();
    });
  }

  bool _isFoundVendor(
    BuildContext context, {
    required String email,
    required String password,
    required List<VendorModel> vendors,
  }) {
    for (var vendor in vendors) {
      if ((vendor.email == email && vendor.password == password) &&
          (vendor.vendorType != VendorType.deleted)) {
        Log.f('Vendor found !');
        authLocalRepo.setVendorData(vendor: vendor);
        return true;
      }
    }

    return false;
  }

  // * Edit Vendor ========================================
  Future<void> editVendor(
    BuildContext context, {
    required Map<String, TextEditingController> controllers,
    required TemplateModel? selectedBusinessType,
    required String pickedImage,
    required String? documentId,
  }) async {
    return await baseFunction(context, () async {
      final vendor = VendorModel(
        documentId: documentId,
        name: controllers[ApiStrings.name]!.text,
        password: controllers[ApiStrings.password]!.text,
        email: controllers[ApiStrings.email]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        address: controllers[ApiStrings.address]!.text,
        businessType: selectedBusinessType?.name,
        paidAmount:
            num.tryParse(controllers[ApiStrings.paidAmount]?.text ?? '0'),
      );

      await authRepository.editVendor(vendor: vendor, pickedImage: pickedImage);
      getCurrentVendor(context);
      if (!context.mounted) return;
    },
        type: FlushBarType.update,
        additionalFunction: (_) => context.toReplacement(const BaseScreen()));
  }

  // * Update Vendor Data ========================================
  Future<void> updateVendorData(
    BuildContext context, {
    bool isBack = true,
    required VendorModel vendor,
  }) async {
    return await baseFunction(
      context,
      () async {
        await authRepository.updateVendorData(vendor: vendor);

        getCurrentVendor(context);
      },
      isBack: isBack,
      type: FlushBarType.update,
    );
  }

  Future<void> updateVendorConfig(
    BuildContext context, {
    required ConfigModel config,
    bool updateCurrentVendor = true,
  }) async {
    return await baseFunction(
      context,
      () async {
        await authRepository.updateVendorConfig(config: config);

        if (updateCurrentVendor) {
          getCurrentVendor(context);
        }
      },
    );
  }

  // * Soft Delete Vendor ========================================
  Future<void> deleteVendor(
    BuildContext context, {
    required int? vendorId,
  }) async {
    return await baseFunction(context, () async {
      final vendor = VendorModel(id: vendorId, vendorType: VendorType.deleted);
      await authRepository.editVendor(vendor: vendor);

      GetStorageHandler.clearLocalData();

      if (!context.mounted) return;
    },
        type: FlushBarType.delete,
        additionalFunction: (_) => context.toReplacement(const LoginScreen()));
  }

  // * Admin Delete Vendor ========================================
  Future<void> adminDeleteVendor(
    BuildContext context, {
    required VendorModel vendor,
  }) async {
    await baseFunction(context, () async {
      await vendorsRepository.deleteConfigByVendorId();

      await vendorsRepository.deletePaymentByVendorId();

      await vendorsRepository.deleteShippingByVendorId();

      await vendorsRepository.deleteTasksByVendorId();
      await vendorsRepository.deleteOrdersByVendorId();
      await vendorsRepository.deleteExpensesByVendorId();
      await vendorsRepository.deleteUsersByVendorId();

      await categoryRepository.deleteCategoriesByVendorId();

      await bannerRepository.deleteBannerByVendorId();

      await vendorsRepository.deleteVendor(
        docId: vendor.documentId,
      );
    }, additionalFunction: (_) {
      context.toReplacement(const VendorsScreen());

      context.showBarMessage(
        context.tr.vendorDeletedSuccessfully,
        isError: true,
      );
    });
  }

  //logout
  Future<void> logout(BuildContext context) async {
    NotificationService.unsubscribeFromTopic(
        VendorModelHelper.currentVendorBusinessName());

    context.to(const LoginScreen());

    GetStorageHandler.clearLocalData();
  }
}
