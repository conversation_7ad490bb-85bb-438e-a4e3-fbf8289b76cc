import 'package:idea2app_vendor_app/src/screens/country/model/country_model.dart';

import '../../../../core/consts/api_strings.dart';
import '../../../currency/model/currency_model.dart';
import '../../../dashboard/models/extra_setting_model.dart';
import 'checkout_settings_model.dart';

class ConfigModel {
  final int? id;
  final String? documentId;
  final int? vendorId;
  final List<CurrencyModel> currencies;
  final List<CountryModel> countries;
  final List<ExtraSettingsModel> sizes;
  final List<ExtraSettingsModel> colors;
  final int bannerLimit;
  final bool showPricing;
  final bool isActiveWorkingTime;
  final num? minimumOrderCost;
  final bool? orderRingingBell;
  final String? primaryColor;
  final String? defaultLanguage;
  final String? defaultTheme;
  final CheckoutSettingsModel? checkoutSettings;

  ConfigModel({
    this.id,
    this.documentId,
    this.vendorId,
    this.minimumOrderCost,
    this.currencies = const [],
    this.sizes = const [],
    this.colors = const [],
    this.countries = const [],
    this.bannerLimit = 0,
    this.showPricing = true,
    this.isActiveWorkingTime = true,
    this.orderRingingBell,
    this.primaryColor,
    this.defaultLanguage,
    this.defaultTheme,
    this.checkoutSettings,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) {
    return ConfigModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      primaryColor: json[ApiStrings.primaryColor],
      defaultLanguage: json[ApiStrings.defaultLanguage] ?? 'en',
      minimumOrderCost: json[ApiStrings.minimumOrderCost] ?? 0,
      isActiveWorkingTime: json[ApiStrings.isActiveWorkingTime] ?? true,
      bannerLimit: json[ApiStrings.bannerLimit] ?? 0,
      countries: json[ApiStrings.countries] != null
          ? (json[ApiStrings.countries] as List)
              .map((e) => CountryModel.fromJson(e))
              .toList()
          : [],
      currencies: json[ApiStrings.currencies] != null
          ? (json[ApiStrings.currencies] as List)
              .map((e) => CurrencyModel.fromJson(e))
              .toList()
          : [],
      sizes: json[ApiStrings.sizes] != null
          ? (json[ApiStrings.sizes] as List)
              .map((e) => ExtraSettingsModel.fromJson(e))
              .toList()
          : [],
      colors: json[ApiStrings.colors] != null
          ? (json[ApiStrings.colors] as List)
              .map((e) => ExtraSettingsModel.fromJson(e))
              .toList()
          : [],
      defaultTheme: json[ApiStrings.defaultTheme] ?? 'light',
      showPricing: json[ApiStrings.showPricing] ?? true,
      orderRingingBell: json[ApiStrings.orderRingingBell],
      checkoutSettings: json[ApiStrings.checkoutSettings] != null
          ? CheckoutSettingsModel.fromJson(json[ApiStrings.checkoutSettings])
          : null,
    );
  }

  Map<String, dynamic> toJson({
    bool fromLocal = false,
  }) {
    return {
      ApiStrings.currencies: fromLocal
          ? currencies.map((e) => e.toJson()).toList()
          : currencies.map((e) => e.id).toList(),
      ApiStrings.countries: fromLocal
          ? countries.map((e) => e.toJson()).toList()
          : countries.map((e) => e.id).toList(),
      if (sizes.isNotEmpty)
        ApiStrings.sizes: sizes.map((e) => e.toJson()).toList(),
      if (colors.isNotEmpty)
        ApiStrings.colors: colors.map((e) => e.toJson()).toList(),
      ApiStrings.bannerLimit: bannerLimit,
      if (vendorId != null) ApiStrings.vendor: vendorId,
      if (documentId != null) ApiStrings.documentId: documentId,
      ApiStrings.showPricing: showPricing,
      ApiStrings.isActiveWorkingTime: isActiveWorkingTime,
      if (minimumOrderCost != null)
        ApiStrings.minimumOrderCost: minimumOrderCost,
      if (orderRingingBell != null)
        ApiStrings.orderRingingBell: orderRingingBell,
      if (primaryColor != null) ApiStrings.primaryColor: primaryColor,
      if (defaultLanguage != null) ApiStrings.defaultLanguage: defaultLanguage,
      if (defaultTheme != null) ApiStrings.defaultTheme: defaultTheme,
      if (checkoutSettings != null)
        ApiStrings.checkoutSettings: checkoutSettings!.toJson(),
    };
  }

  Map<String, dynamic> toCheckoutSettingsJson() {
    return {
      if (documentId != null) ApiStrings.documentId: documentId,
      ApiStrings.checkoutSettings: checkoutSettings!.toJson(),
    };
  }

  ConfigModel copyWith({
    int? id,
    String? documentId,
    int? vendorId,
    String? primaryColor,
    String? defaultLanguage,
    String? defaultTheme,
    num? minimumOrderCost,
    List<CurrencyModel>? currencies,
    List<ExtraSettingsModel>? sizes,
    List<ExtraSettingsModel>? colors,
    List<CountryModel>? countries,
    int? bannerLimit,
    bool? showPricing,
    bool? isActiveWorkingTime,
    bool? orderRingingBell,
    CheckoutSettingsModel? checkoutSettings,
  }) {
    return ConfigModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      vendorId: vendorId ?? this.vendorId,
      minimumOrderCost: minimumOrderCost ?? this.minimumOrderCost,
      currencies: currencies ?? this.currencies,
      primaryColor: primaryColor ?? this.primaryColor,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      defaultTheme: defaultTheme ?? this.defaultTheme,
      sizes: sizes ?? this.sizes,
      colors: colors ?? this.colors,
      countries: countries ?? this.countries,
      bannerLimit: bannerLimit ?? this.bannerLimit,
      showPricing: showPricing ?? this.showPricing,
      isActiveWorkingTime: isActiveWorkingTime ?? this.isActiveWorkingTime,
      orderRingingBell: orderRingingBell ?? this.orderRingingBell,
      checkoutSettings: checkoutSettings ?? this.checkoutSettings,
    );
  }
}

class SizeModel {
  final String? name;
  final List? categories;

  SizeModel({
    this.name,
    this.categories,
  });

  factory SizeModel.fromJson(Map<String, dynamic> json) {
    return SizeModel(
      name: json[ApiStrings.name],
      categories: json[ApiStrings.name],
    );
  }
}
