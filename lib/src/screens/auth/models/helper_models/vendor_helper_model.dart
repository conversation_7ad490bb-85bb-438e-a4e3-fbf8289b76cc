import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/data/local/local_keys.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/role_model.dart';

String getCurrentDeviceType() {
  return kIsWeb
      ? "Web"
      : Platform.isIOS
          ? "iOS"
          : "Android";
}

extension VendorModelHelper on VendorModel {
  static Map<String, dynamic> currentVendor() {
    final vendor = jsonDecode(
      GetStorageHandler.getLocalData(key: LocalKeys.vendorData) ?? '{}',
    );

    return vendor ?? {};
  }

  static VendorModel currentVendorModel() {
    final vendor = currentVendor();

    return VendorModel.fromJson(vendor);
  }

  static currentVendorId() {
    final vendor = currentVendor();

    final vendorId = vendor['id'];

    return vendorId;
  }

  static isAdmin() {
    final vendorName = currentVendorBusinessName();

    return vendorName == 'default';
  }

  static currentVendorBusinessName() {
    final vendor = currentVendor();

    final vendorId = vendor['business_name'];

    return vendorId;
  }

  static currentVendorDocumentId() {
    final vendor = currentVendor();

    final vendorId = vendor['documentId'];

    return vendorId;
  }

  static int currentVendorCountryId() {
    final vendor = currentVendor();

    final countries = (vendor['countries'] ?? []) as List;

    final countryId = countries.firstOrNull;

    log('COUNTRY_ID $countryId');

    return countryId ?? 1;
  }

  static String currentVendorType() {
    final vendor = currentVendor();

    final vendorType = vendor['type'] ?? 'free';

    return vendorType;
  }

  static VendorType getVendorType(String type) {
    switch (type) {
      case 'free':
        return VendorType.free;
      case 'deleted':
        return VendorType.deleted;
      case 'monthly':
        return VendorType.monthly;
      case 'quarter':
        return VendorType.quarter;
      case 'semester':
        return VendorType.semester;
      case 'annually':
        return VendorType.annually;
      default:
        return VendorType.free;
    }
  }

  static String vendorTypeToString(VendorType type) {
    switch (type) {
      case VendorType.free:
        return 'free';
      case VendorType.deleted:
        return 'deleted';
      case VendorType.monthly:
        return 'monthly';
      case VendorType.quarter:
        return 'quarter';
      case VendorType.semester:
        return 'semester';
      case VendorType.annually:
        return 'annually';
      default:
        return 'free';
    }
  }

  static String vendorTypePlan(VendorType? type, BuildContext context) {
    if (context.isEng) {
      switch (type) {
        case VendorType.free:
          return '${context.tr.free} ${context.tr.plan}';
        case VendorType.deleted:
          return '${context.tr.deleted} ${context.tr.plan}';
        case VendorType.monthly:
          return '${context.tr.monthly} ${context.tr.plan}';
        case VendorType.quarter:
          return '${context.tr.threeMonths} ${context.tr.plan}';
        case VendorType.semester:
          return '${context.tr.sixMonths} ${context.tr.plan}';
        case VendorType.annually:
          return '${context.tr.annually} ${context.tr.plan}';
        default:
          return '${context.tr.free} ${context.tr.plan}';
      }
    }
    switch (type) {
      case VendorType.free:
        return '${context.tr.plan} ${context.tr.free}';
      case VendorType.deleted:
        return '${context.tr.plan} ${context.tr.deleted}';
      case VendorType.monthly:
        return '${context.tr.plan} ${context.tr.monthly}';
      case VendorType.quarter:
        return '${context.tr.plan} ${context.tr.threeMonths}';
      case VendorType.semester:
        return '${context.tr.plan} ${context.tr.sixMonths}';
      case VendorType.annually:
        return '${context.tr.plan} ${context.tr.annually}';
      default:
        return '${context.tr.plan} ${context.tr.free}';
    }
  }

  // ! isAdmin (if roles null or empty)
  static bool isParentVendor() {
    final vendor = currentVendorModel();
    return vendor.parentVendor == null;
  }

  // * Role-Based Access Control ========================================

  /// Check if current vendor has a specific role
  static bool hasRole(RoleEnum role) {
    final vendor = currentVendorModel();
    if (isParentVendor()) return true;

    if (vendor.roles == null || vendor.roles!.isEmpty) return false;

    // If it's a main vendor (no parent), they have all permissions
    // if (vendor.parentVendor == null) return true;

    // For sub-vendors, check their specific roles
    return vendor.roles?.contains(role) ?? false;
  }

  /// Check if current vendor has any of the specified roles
  static bool hasAnyRole(List<RoleEnum> roles) {
    return roles.any((role) => hasRole(role));
  }

  /// Check if current vendor has all of the specified roles
  static bool hasAllRoles(List<RoleEnum> roles) {
    return roles.every((role) => hasRole(role));
  }

  /// Check if current vendor can view orders
  static bool canViewOrders() {
    return hasAnyRole([RoleEnum.viewOrders, RoleEnum.manageOrders]);
  }

  /// Check if current vendor can manage orders
  static bool canManageOrders() {
    return hasRole(RoleEnum.manageOrders);
  }

  /// Check if current vendor can view categories and products
  static bool canViewCategoriesProducts() {
    return hasAnyRole(
        [RoleEnum.viewCategoriesProducts, RoleEnum.manageCategoriesProducts]);
  }

  /// Check if current vendor can manage categories and products
  static bool canManageCategoriesProducts() {
    return hasRole(RoleEnum.manageCategoriesProducts);
  }

  /// Check if current vendor can create store orders
  static bool canCreateStoreOrders() {
    return hasRole(RoleEnum.createStoreOrders);
  }

  /// Check if current vendor can manage store orders
  static bool canManageStoreOrders() {
    return hasRole(RoleEnum.manageStoreOrders);
  }

  /// Check if current vendor can access app settings
  static bool canAccessAppSettings() {
    return hasRole(RoleEnum.accessAppSettings);
  }

  /// Check if current vendor can access orders and shipping
  static bool canAccessOrdersShipping() {
    return hasRole(RoleEnum.accessOrdersShipping);
  }

  /// Check if current vendor can access store appearance
  static bool canAccessStoreAppearance() {
    return hasRole(RoleEnum.accessStoreAppearance);
  }

  /// Check if current vendor can access payments and promotions
  static bool canAccessPaymentsPromotions() {
    return hasRole(RoleEnum.accessPaymentsPromotions);
  }

  /// Check if current vendor can access users
  static bool canAccessUsers() {
    return hasRole(RoleEnum.accessUsers);
  }

  /// Check if current vendor can access reports and operations
  static bool canAccessReportsOperations() {
    return hasRole(RoleEnum.accessReportsOperations);
  }

  /// Check if current vendor can manage employees
  static bool canManageEmployees() {
    return hasRole(RoleEnum.manageEmployees);
  }

  /// Check if current vendor is a main vendor (has no parent)
  static bool isMainVendor() {
    final vendor = currentVendorModel();
    return vendor?.parentVendor == null;
  }

  /// Check if current vendor is a sub-vendor (has a parent)
  static bool isSubVendor() {
    final vendor = currentVendorModel();
    return vendor?.parentVendor != null;
  }

  // * Navigation Screen Management ========================================

  /// Get list of available screen types based on current user's roles
  static List<NavigationScreenType> getAvailableScreens() {
    List<NavigationScreenType> screens = [];

    // Home is always available
    screens.add(NavigationScreenType.home);

    // Categories - available if can view categories and products
    if (canViewCategoriesProducts()) {
      screens.add(NavigationScreenType.categories);
    }

    // Orders - available if can view orders
    if (canViewOrders()) {
      screens.add(NavigationScreenType.orders);
    }

    // Dashboard is always available (content will be filtered)
    screens.add(NavigationScreenType.dashboard);

    return screens;
  }

  /// Get screen type by index from available screens
  static NavigationScreenType? getScreenTypeByIndex(int index) {
    final availableScreens = getAvailableScreens();
    if (index >= 0 && index < availableScreens.length) {
      return availableScreens[index];
    }
    return null;
  }
}

enum NavigationScreenType {
  home,
  categories,
  orders,
  dashboard,
}
