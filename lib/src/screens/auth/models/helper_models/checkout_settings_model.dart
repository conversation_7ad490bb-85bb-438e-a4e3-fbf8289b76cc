import '../../../../core/consts/api_strings.dart';

enum CheckoutType { steps, onePage }

class CheckoutSettingsModel {
  final int? id;
  final CheckoutType checkoutType;
  final bool showCheckout;
  final bool guestCheckout;
  final CheckoutFieldModel email;
  final CheckoutFieldModel name;
  final CheckoutFieldModel phone;

  const CheckoutSettingsModel({
    this.id,
    this.checkoutType = CheckoutType.steps,
    this.showCheckout = true,
    this.guestCheckout = true,
    this.email = const CheckoutFieldModel(),
    this.name = const CheckoutFieldModel(),
    this.phone = const CheckoutFieldModel(),
  });

  factory CheckoutSettingsModel.fromJson(Map<String, dynamic> json) {
    return CheckoutSettingsModel(
      id: json[ApiStrings.id],
      checkoutType: _parseCheckoutType(json[ApiStrings.checkoutType]),
      showCheckout: json[ApiStrings.showCheckout] ?? true,
      guestCheckout: json[ApiStrings.guestCheckout] ?? true,
      email: json[ApiStrings.email] != null
          ? CheckoutFieldModel.fromJson(json[ApiStrings.email])
          : const CheckoutFieldModel(),
      name: json[ApiStrings.name] != null
          ? CheckoutFieldModel.fromJson(json[ApiStrings.name])
          : const CheckoutFieldModel(),
      phone: json[ApiStrings.phone] != null
          ? CheckoutFieldModel.fromJson(json[ApiStrings.phone])
          : const CheckoutFieldModel(),
    );
  }

  static CheckoutType _parseCheckoutType(String? type) {
    switch (type) {
      case 'one_page':
        return CheckoutType.onePage;
      case 'steps':
      default:
        return CheckoutType.steps;
    }
  }

  String get checkoutTypeString {
    switch (checkoutType) {
      case CheckoutType.onePage:
        return 'one_page';
      case CheckoutType.steps:
        return 'steps';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      // if (id != null) ApiStrings.id: id,
      ApiStrings.checkoutType: checkoutTypeString,
      ApiStrings.showCheckout: showCheckout,
      ApiStrings.guestCheckout: guestCheckout,
      ApiStrings.email: email.toJson(),
      ApiStrings.name: name.toJson(),
      ApiStrings.phone: phone.toJson(),
    };
  }

  CheckoutSettingsModel copyWith({
    int? id,
    CheckoutType? checkoutType,
    bool? showCheckout,
    bool? guestCheckout,
    CheckoutFieldModel? email,
    CheckoutFieldModel? name,
    CheckoutFieldModel? phone,
  }) {
    return CheckoutSettingsModel(
      id: id ?? this.id,
      checkoutType: checkoutType ?? this.checkoutType,
      showCheckout: showCheckout ?? this.showCheckout,
      guestCheckout: guestCheckout ?? this.guestCheckout,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
    );
  }
}

class CheckoutFieldModel {
  final int? id;
  final bool show;
  final bool isRequired;

  const CheckoutFieldModel({
    this.id,
    this.show = true,
    this.isRequired = true,
  });

  factory CheckoutFieldModel.fromJson(Map<String, dynamic> json) {
    return CheckoutFieldModel(
      id: json[ApiStrings.id],
      show: json[ApiStrings.show] ?? true,
      isRequired: json[ApiStrings.isRequired] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.show: show,
      ApiStrings.isRequired: isRequired,
    };
  }

  CheckoutFieldModel copyWith({
    int? id,
    bool? show,
    bool? isRequired,
  }) {
    return CheckoutFieldModel(
      id: id ?? this.id,
      show: show ?? this.show,
      isRequired: isRequired ?? this.isRequired,
    );
  }
}
