import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/widgets/products_by_category_grid_view.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/widgets/products_header_section.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart' show AppSpaces;
import '../../../../core/services/notifications_service.dart';
import '../../../../core/shared_widgets/animated/empty_data_widget.dart';
import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../categories/models/category_model.dart';
import '../../../categories/view/widgets/limit_dialog_widget.dart';
import '../add_product/add_product.dart';

class ProductsScreen extends HookWidget {
  final CategoryModel category;

  const ProductsScreen({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    final productsVM = context.read<ProductVM>();

    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        productsVM.getProductsByCategory(context, catId: category.documentId!);
      });

      return () {};
    }, []);

    return Consumer2<AuthVM, ProductVM>(
      builder: (context, authVM, productsVM, child) {
        final reachLimit = authVM.isFree &&
            productsVM.productsByCategory.length > AppConsts.maxProductsLimit;

        final globalSale = authVM.currentVendor?.isGlobalSalePercentage == true
            ? '${authVM.currentVendor?.globalSale}%'
            : '${authVM.currentVendor?.globalSale} ${authVM.currentCurrency(context)}';

        final isGlobalSaleNotEqualZero =
            authVM.currentVendor?.globalSale != null &&
                authVM.currentVendor?.globalSale != 0;

        void navigateToProduct() {
          if (authVM.isFree && reachLimit) {
            NotificationService.sendNotification(
              title: "Limit Reached",
              body: "${authVM.currentVendor?.name} Products Limit Reached",
              userTokenOrTopic: AppConsts.adminTopic, //? Admins
              isTopic: true,
            );
            showDialog(
                context: context, builder: (context) => LimitDialogWidget());

            return;
          } else {
            context.to(AddProductScreen(category: category));
          }
        }

        return RefreshIndicatorWidget(
            onRefresh: () {
              return productsVM.getProductsByCategory(
                context,
                catId: category.documentId!,
              );
            },
            child: Scaffold(
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.endFloat,
              // Add product button - only show if user can manage categories and products
              floatingActionButton:
                  VendorModelHelper.canManageCategoriesProducts()
                      ? FloatingActionButton.extended(
                          elevation: 0,
                          onPressed: navigateToProduct,
                          backgroundColor: ColorManager.primaryColor,
                          icon: Icon(
                            Icons.add,
                            color: ColorManager.white,
                            // size: 14,
                          ),
                          label: Text(
                            context.tr.addProduct,
                            style: context.labelMedium.copyWith(
                              fontWeight: FontWeight.w500,
                              color: ColorManager.white,
                            ),
                          ),
                        )
                      : null,
              appBar: MainAppBar(
                isCenterTitle: false,
                title: '${category.nameByLang(context)} ${context.tr.products}',
                haveBackButton: true,
                actionWidget: Row(
                  mainAxisSize: MainAxisSize.min,
                ),
              ),
              body: Builder(builder: (context) {
                final products = productsVM.searchedProducts;

                if (products.isEmpty || productsVM.isLoading) {
                  return ListView(
                    children: [
                      ProductsHeaderSection(
                        queryController: queryController,
                        isSearch: isSearch,
                        category: category,
                      ),
                      context.largeGap,
                      EmptyDataWidget(
                        isLoading: productsVM.isLoading,
                        message: context.tr.noProductsInThisCategory,
                      ),
                    ],
                  );
                } else {
                  return ListView(
                    children: [
                      ProductsHeaderSection(
                        queryController: queryController,
                        isSearch: isSearch,
                        category: category,
                      ),
                      if (isGlobalSaleNotEqualZero) ...[
                        context.mediumGap,
                        Container(
                          padding: EdgeInsets.all(AppSpaces.mediumPadding),
                          margin: EdgeInsets.symmetric(
                              horizontal: AppSpaces.mediumPadding),
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(AppRadius.baseRadius),
                            gradient: LinearGradient(
                                colors: ColorManager.activeGradientBackground,
                                begin: const FractionalOffset(0.0, 0.0),
                                end: const FractionalOffset(1.0, 0.0),
                                stops: const [0.0, 1.0],
                                tileMode: TileMode.clamp),
                          ),
                          child: Row(
                            children: [
                              Text('🔥', style: context.title),
                              context.smallGap,
                              Expanded(
                                child: Text(
                                    context.tr
                                        .globalOfferDecRimender(globalSale),
                                    style: context.labelMedium
                                        .copyWith(color: ColorManager.white)),
                              ),
                            ],
                          ),
                        ),
                      ],
                      context.largeGap,
                      ProductsByCategoryGridView(
                        categoryModel: category,
                        products: productsVM.searchedProducts,
                      ),
                    ],
                  );
                }
              }),
            ));
      },
    );
  }
}
