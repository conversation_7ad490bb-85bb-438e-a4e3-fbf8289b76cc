import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

import '../../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../../../core/shared_widgets/html_editor/html_editor_widget.dart';

class ProductBasicInfo extends HookWidget {
  final Map<String, TextEditingController> controllers;

  const ProductBasicInfo({super.key, required this.controllers});

  @override
  Widget build(BuildContext context) {
    final icon = Icon(
      CupertinoIcons.doc_text,
      color: context.isDark ? CupertinoColors.white : CupertinoColors.black,
    );

    return Column(
      children: [
        //! English Name
        BaseTextField(
            title: context.tr.englishName,
            hint: context.tr.englishName,
            controller: controllers[ApiStrings.title],
            isRequired: false,
            icon: icon),
        context.fieldsGap,

        //! Arabic Name
        BaseTextField(
            title: context.tr.arabicName,
            hint: context.tr.arabicName,
            isRequired: false,
            controller: controllers[ApiStrings.arabicTitle],
            icon: icon),

        context.fieldsGap,

        //! English Description
        HtmlEditorWidget(
          controller: controllers[ApiStrings.description]!,
          title:
              '${context.tr.englishDescription} (${context.tr.customized_description})',
          hint: context.tr.englishDescription,
          isRequired: false,
          icon: Icon(
            Icons.comment_outlined,
            color:
                context.isDark ? CupertinoColors.white : CupertinoColors.black,
          ),
        ),

        context.fieldsGap,

        //! Arabic Description
        HtmlEditorWidget(
          controller: controllers[ApiStrings.arabicDescription]!,
          title:
              '${context.tr.arabicDescription} (${context.tr.customized_description})',
          hint: context.tr.arabicDescription,
          isRequired: false,
          icon: Icon(
            Icons.comment_outlined,
            color:
                context.isDark ? CupertinoColors.white : CupertinoColors.black,
          ),
        ),
      ],
    );
  }
}
