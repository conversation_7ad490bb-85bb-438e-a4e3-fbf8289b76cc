import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/lists/base_list.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/employee/view_model/employee_view_model.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import 'add_employee/add_employee_basic_info_screen.dart';
import 'add_employee/add_employee_permissions_screen.dart';
import 'widgets/employee_card.dart';

class EmployeesScreen extends HookWidget {
  const EmployeesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final employeeVM = context.read<EmployeeVM>();
    final searchController = useTextEditingController();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        employeeVM.getEmployees(context);
      });
      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        haveBackButton: true,
        title: context.tr.addAndManageEmployees,
      ),
      body: Consumer<EmployeeVM>(
        builder: (context, employeeVM, child) {
          return Column(
            children: [
              // Search Field
              Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: BaseTextField(
                  controller: searchController,
                  withoutEnter: true,
                  hint: context.tr.searchByNameOrEmail,
                  icon: const Icon(Icons.search),
                  onChanged: (value) {
                    employeeVM.searchEmployees(value);
                  },
                ),
              ),

              // Employees List
              Expanded(
                child: BaseList<VendorModel>(
                  data: employeeVM.filteredEmployees,
                  isLoading: employeeVM.isLoading,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding,
                  ),
                  itemBuilder: (employee, index) {
                    return EmployeeCard(
                      onShare: () {
                        Share.share(
                          "${context.tr.email}: ${employee.email} \n${context.tr.password} : ${employee.password}",
                        );
                      },
                      employee: employee,
                      onEditBasic: () {
                        context.to(AddEmployeeBasicInfoScreen(
                          employee: employee,
                          isEdit: true,
                        ));
                      },
                      onEditPermissions: () {
                        context.to(AddEmployeePermissionsScreen(
                          employee: employee,
                          isEdit: true,
                        ));
                      },
                      onDelete: () {
                        _showDeleteDialog(context, employeeVM, employee);
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: ColorManager.primaryColor,
        elevation: 0,
        onPressed: () {
          context.to(const AddEmployeeBasicInfoScreen());
        },
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
    );
  }

  void _showDeleteDialog(
      BuildContext context, EmployeeVM employeeVM, VendorModel employee) {
    showAlertDialog(
      context,
      child:
          Text(context.tr.areYouSureDeleteEmployee, style: context.labelLarge),
      isLoading: employeeVM.isLoading,
      onConfirm: () async {
        Navigator.pop(context);
        await employeeVM.deleteEmployee(context, employee: employee);
      },
    );
  }
}
