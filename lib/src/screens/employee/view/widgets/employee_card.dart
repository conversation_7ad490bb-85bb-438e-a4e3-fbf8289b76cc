import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/role_model.dart';

class EmployeeCard extends StatelessWidget {
  final VendorModel employee;
  final VoidCallback onEditBasic;
  final VoidCallback onEditPermissions;
  final VoidCallback onDelete;
  final VoidCallback onShare;

  const EmployeeCard({
    super.key,
    required this.employee,
    required this.onEditBasic,
    required this.onEditPermissions,
    required this.onDelete,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.circular(AppRadius.baseRadius),
        boxShadow: [
          BoxShadow(
            color: ColorManager.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with avatar and actions
          Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 25,
                backgroundColor: ColorManager.primaryColor.withOpacity(0.1),
                child: employee.logo != null && employee.logo!.url!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: Image.network(
                          employee.logo!.url!,
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Text(
                        _getInitials(employee.name ?? ''),
                        style: context.labelLarge.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),

              const SizedBox(width: AppSpaces.mediumPadding),

              // Employee info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee.name ?? context.tr.noName,
                      style: context.labelLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    context.xSmallGap,
                    Text(employee.email ?? context.tr.noEmail,
                        style: context.labelMedium),
                  ],
                ),
              ),

              // Actions
              PopupMenuButton<String>(
                color: context.appTheme.cardColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                ),
                onSelected: (value) {
                  switch (value) {
                    case 'edit_basic':
                      onEditBasic();
                      break;
                    case 'edit_permissions':
                      onEditPermissions();
                      break;

                    case 'share':
                      onShare();
                      break;
                    case 'delete':
                      onDelete();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit_basic',
                    child: Row(
                      children: [
                        Icon(Icons.person_outline,
                            color: ColorManager.primaryColor),
                        const SizedBox(width: AppSpaces.smallPadding),
                        Text(context.tr.editBasicInfo),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'edit_permissions',
                    child: Row(
                      children: [
                        Icon(Icons.security, color: ColorManager.primaryColor),
                        const SizedBox(width: AppSpaces.smallPadding),
                        Text(context.tr.editPermissions),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, color: ColorManager.primaryColor),
                        const SizedBox(width: AppSpaces.smallPadding),
                        Text(context.tr.share),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: ColorManager.red),
                        const SizedBox(width: AppSpaces.smallPadding),
                        Text(context.tr.delete),
                      ],
                    ),
                  ),
                ],
                child: Icon(
                  Icons.more_vert,
                  color: ColorManager.iconColor,
                ),
              ),
            ],
          ),

          context.mediumGap,

          // Status and roles
          Row(
            children: [
              // Roles count
              if (employee.roles?.isNotEmpty == true) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.smallPadding,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  child: Text(
                    '${employee.roles!.length} ${context.tr.roles}',
                    style: context.labelSmall.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
              ],

              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.smallPadding,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: (employee.isActive ?? false)
                      ? ColorManager.successColor.withOpacity(0.1)
                      : ColorManager.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: Text(
                  (employee.isActive ?? false)
                      ? context.tr.active
                      : context.tr.inactive,
                  style: context.labelSmall.copyWith(
                    color: (employee.isActive ?? false)
                        ? ColorManager.successColor
                        : ColorManager.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          // Roles list (if any)
          if (employee.roles?.isNotEmpty == true) ...[
            context.smallGap,
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: employee.roles!.take(3).map((role) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  child: Text(
                    role.toRoleString(context),
                    style: context.hint.copyWith(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
            if (employee.roles!.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '+${employee.roles!.length - 3} ${context.tr.more}',
                  style: context.hint.copyWith(fontSize: 10),
                ),
              ),
          ],
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    final words = name.split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    }
    return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'
        .toUpperCase();
  }
}
