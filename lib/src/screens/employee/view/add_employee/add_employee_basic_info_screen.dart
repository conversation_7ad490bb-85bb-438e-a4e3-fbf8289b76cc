import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/employee/view/add_employee/add_employee_permissions_screen.dart';
import 'package:idea2app_vendor_app/src/screens/employee/view_model/employee_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/theme/theme.dart';

class AddEmployeeBasicInfoScreen extends HookWidget {
  final VendorModel? employee;
  final bool isEdit;

  const AddEmployeeBasicInfoScreen({
    super.key,
    this.employee,
    this.isEdit = false,
  });

  @override
  Widget build(BuildContext context) {
    final formKey = useState(GlobalKey<FormState>());
    final employeeVM = context.read<EmployeeVM>();

    final nameController = useTextEditingController(
      text: isEdit ? employee?.name ?? '' : '',
    );
    final emailController = useTextEditingController(
      text: isEdit ? employee?.email ?? '' : '@idea2app.tech',
    );
    final passwordController = useTextEditingController(
      text: isEdit ? employee?.password ?? '' : '',
    );
    final isActive = useState<bool>(isEdit ? employee?.isActive ?? true : true);

    // Auto-generate email from name (only for new employees)
    useEffect(() {
      void updateEmail() {
        if (!isEdit && nameController.text.isNotEmpty) {
          final formattedName = nameController.text
              .toLowerCase()
              .replaceAll(' ', '-')
              .replaceAll(RegExp(r'[^a-zA-Z0-9-_]'), '');
          emailController.text = '$<EMAIL>';
        }
      }

      if (!isEdit) {
        nameController.addListener(updateEmail);
        return () => nameController.removeListener(updateEmail);
      }
      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        title: isEdit ? context.tr.editEmployee : context.tr.addEmployee,
        haveBackButton: true,
      ),
      body: Form(
        key: formKey.value,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              context.mediumGap,

              //! Name Field
              BaseTextField(
                controller: nameController,
                title: context.tr.name,
                textInputType: TextInputType.name,
                hint: context.tr.name,
              ),

              context.largeGap,

              //! Email Field
              BaseTextField(
                controller: emailController,
                title: context.tr.email,
                hint: context.tr.email,
                textInputType: TextInputType.emailAddress,
                enabled: isEdit, // Only editable in edit mode
              ),

              context.largeGap,

              //! Password Field
              BaseTextField(
                controller: passwordController,
                title: context.tr.password,
                textInputType: TextInputType.visiblePassword,
                hint: context.tr.password,
              ),

              context.largeGap,

              //! Active Toggle (only in edit mode)
              if (isEdit) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.xSmallPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr.active,
                        style: context.labelLarge.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Switch(
                        activeColor: ColorManager.primaryColor,
                        value: isActive.value,
                        onChanged: (value) {
                          isActive.value = value;
                        },
                      ),
                    ],
                  ),
                ),
              ],

              context.xLargeGap,

              Consumer<EmployeeVM>(
                builder: (context, employeeVM, child) {
                  return Button(
                    label: isEdit ? context.tr.save : context.tr.next,
                    haveElevation: false,
                    isLoading: employeeVM.isLoading,
                    onPressed: () async {
                      if (!formKey.value.currentState!.validate()) return;

                      // Check email validation for both new and edited employees
                      final emailExists = await employeeVM.checkEmailExists(
                        context,
                        emailController.text,
                        excludeEmployeeId: isEdit ? employee?.documentId : null,
                      );
                      if (emailExists) {
                        context.showBarMessage(
                          context.tr.emailAlreadyExist,
                          isError: true,
                        );
                        return;
                      }

                      if (isEdit) {
                        // Update employee basic info
                        final updatedEmployee = employee!.copyWith(
                          name: nameController.text,
                          email: emailController.text,
                          password: passwordController.text.isNotEmpty
                              ? passwordController.text
                              : employee!.password,
                          isActive: isActive.value,
                        );

                        await employeeVM.editEmployee(
                          context,
                          employee: updatedEmployee,
                          roles: employee!.roles ?? [],
                        );
                      } else {
                        // Pass controllers to permissions screen
                        final controllers = {
                          'name': nameController,
                          'email': emailController,
                          'password': passwordController,
                        };

                        context.to(AddEmployeePermissionsScreen(
                          controllers: controllers,
                        ));
                      }
                    },
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
