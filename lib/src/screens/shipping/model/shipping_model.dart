import 'dart:developer';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';

import 'city_cost_model.dart';

class ShippingModel {
  final int? id;
  final String? documentId;
  final String? title;
  final String? description;
  final num? cost;
  final int? vendorId;

  final List<CityCostModel>? citiesCost;

  const ShippingModel({
    this.id,
    this.documentId,
    this.title,
    this.description,
    this.cost,
    this.vendorId,
    this.citiesCost = const [],
  });

  factory ShippingModel.fromJson(Map<String, dynamic> json) {
    final citiesCostList = json[ApiStrings.citiesCost] as List?;
    final citiesCost =
        citiesCostList?.map((e) => CityCostModel.fromJson(e)).toList();

    log('VendorCities ${json[ApiStrings.citiesCost]}');

    return ShippingModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      description: json[ApiStrings.description],
      cost: json[ApiStrings.cost],
      citiesCost: citiesCost,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (documentId != null) ApiStrings.documentId: documentId,
      ApiStrings.title: title,
      ApiStrings.cost: cost,
      if (vendorId != null) ApiStrings.vendor: vendorId,
      if (citiesCost != null)
        ApiStrings.citiesCost: citiesCost?.map((e) => e.toJson()).toList(),
    };
  }

  Map<String, dynamic> toCityCostJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.documentId: documentId,
      ApiStrings.citiesCost: citiesCost?.map((e) => e.toJson()).toList(),
    };
  }

  //! Copy with
  ShippingModel copyWith({
    String? documentId,
    String? title,
    String? description,
    num? cost,
    int? vendorId,
    List<CityCostModel>? citiesCost,
  }) {
    return ShippingModel(
      documentId: documentId ?? this.documentId,
      title: title ?? this.title,
      description: description ?? this.description,
      cost: cost ?? this.cost,
      vendorId: vendorId ?? this.vendorId,
      citiesCost: citiesCost ?? this.citiesCost,
    );
  }

  @override
  String toString() => 'SerializerShipping { id: $documentId title $title}';
}
