import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/save_shipping_floating_button.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/shipping_list.dart';

import '../../../core/shared_widgets/appbar/main_appbar.dart';

class ShippingScreen extends HookWidget {
  const ShippingScreen({super.key});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
        appBar: MainAppBar(
          title: context.tr.shippingCost,
          haveBackButton: true,
          isCenterTitle: false,
          actionWidget: const SaveShippingFloatingButton(),
          onBackPressed: () {
            context.back();
          },
        ),
        body: const ShippingList());
  }
}
