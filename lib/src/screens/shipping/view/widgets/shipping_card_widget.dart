import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/areas_screen.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view_model/shipping_view_model.dart';
import 'package:provider/provider.dart';

import '../../model/city_cost_model.dart';

class ShippingCardWidget extends HookWidget {
  final CityCostModel cityCost;
  final ValueNotifier<String> costValue;
  final ValueNotifier<bool> freeShipping;
  final ValueNotifier<bool> isActive;

  const ShippingCardWidget({
    super.key,
    required this.cityCost,
    required this.costValue,
    required this.freeShipping,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    final vendorVM = context.read<AuthVM>();
    final shippingVM = context.read<ShippingVM>();

    return Container(
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        boxShadow: context.isDark
            ? ConstantsWidgets.darkBoxShadowFromBottom
            : ConstantsWidgets.boxShadowFromBottom,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.isEng
                    ? cityCost.city?.name ?? ''
                    : cityLangToAr(cityCost.city?.name ?? ''),
                style: context.title,
              ),
              TextButton(
                  onPressed: () {
                    context.to(AreasScreen(
                      cityName: cityCost.city?.name ?? '',
                      cityDocumentIdId: cityCost.city?.documentId ?? '',
                    ));
                  },
                  child: Row(
                    children: [
                      Text(
                        context.tr.areas,
                        style: context.labelMedium,
                      ),
                      context.smallGap,
                      InkWell(
                        child: CircleAvatar(
                          radius: 10,
                          backgroundColor: ColorManager.primaryColor,
                          child: Icon(
                            Icons.add,
                            color: ColorManager.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ))
            ],
          ),
          Divider(),

          //* is Active
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.tr.active,
                style: context.title,
              ),
              ValueListenableBuilder<bool>(
                valueListenable: isActive,
                builder: (context, isActiveValue, child) {
                  return SwitchButtonWidget(
                    value: isActive,
                    onChanged: (value) {
                      isActive.value = value;
                      shippingVM.onChangeCost(
                        cityId: cityCost.city?.documentId ?? '',
                        cost: costValue.value,
                        freeShipping: freeShipping.value,
                        isActive: value,
                      );
                    },
                  );
                },
              ),
            ],
          ),
          //* freeShipping
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.tr.freeShipping,
                style: context.title,
              ),
              ValueListenableBuilder<bool>(
                valueListenable: freeShipping,
                builder: (context, isFreeShippingValue, child) {
                  return SwitchButtonWidget(
                    value: freeShipping,
                    onChanged: (value) {
                      freeShipping.value = value;
                      shippingVM.onChangeCost(
                        cityId: cityCost.city?.documentId ?? '',
                        cost: costValue.value,
                        freeShipping: value,
                        isActive: isActive.value,
                      );
                    },
                  );
                },
              ),
            ],
          ),

          context.mediumGap,
          BaseTextField(
            textInputType: TextInputType.number,
            initialValue: costValue.value,
            onChanged: (value) {
              if (value.isEmpty) return;
              shippingVM.onChangeCost(
                cityId: cityCost.city?.documentId ?? '',
                cost: value,
                freeShipping: freeShipping.value,
                isActive: isActive.value,
              );
              costValue.value = value;
            },
            suffixIcon: Padding(
              padding: const EdgeInsets.all(15),
              child: Text(
                vendorVM.currentCurrency(context),
                style: context.labelMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

String cityLangToAr(String? enCityName) {
  enCityName = enCityName?.toLowerCase();
  switch (enCityName) {
    case 'alexandria':
      return 'الإسكندرية';
    case 'aswan':
      return 'أسوان';
    case 'asyut':
      return 'أسيوط';
    case 'beheira':
      return 'البحيرة';
    case 'beni suef':
      return 'بني سويف';
    case 'cairo':
      return 'القاهرة';
    case 'dakahlia':
      return 'الدقهلية';
    case 'damietta':
      return 'دمياط';
    case 'fayoum':
      return 'الفيوم';
    case 'gharbia':
      return 'الغربية';
    case 'giza':
      return 'الجيزة';
    case 'ismailia':
      return 'الإسماعيلية';
    case 'kafr el-sheikh':
      return 'كفر الشيخ';
    case 'matrouh':
      return 'مطروح';
    case 'minya':
      return 'المنيا';
    case 'monufia':
      return 'المنوفية';
    case 'el wadi el gedid':
      return 'الوادي الجديد';
    case 'north sinai':
      return 'شمال سيناء';
    case 'port said':
      return 'بورسعيد';
    case 'qalyubia':
      return 'القليوبية';
    case 'qena':
      return 'قنا';
    case 'red sea':
      return 'البحر الأحمر';
    case 'sharqia':
      return 'الشرقية';
    case 'sohag':
      return 'سوهاج';
    case 'south sinai':
      return 'جنوب سيناء';
    case 'suez':
      return 'السويس';
    case 'luxor':
      return 'الأقصر';
    default:
      return enCityName ?? 'Unknown';
  }
}

//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
// import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
// import 'package:idea2app_vendor_app/src/screens/shipping/view_model/shipping_view_model.dart';
// import 'package:provider/provider.dart';
//
// import '../../model/city_cost_model.dart';
//
// class ShippingCardWidget extends HookWidget {
//   final CityCostModel cityCost;
//   final ValueNotifier costValue;
//
//   const ShippingCardWidget({
//     super.key,
//     required this.cityCost,
//     required this.costValue,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final vendorVM = context.read<AuthVM>();
//     final shippingVM = context.read<ShippingVM>();
//
//     // final costController =
//     //     useTextEditingController(text: cityCost.cost?.toString() ?? '0');
//
//     return Row(
//       children: [
//         Expanded(
//             child: Text(
//               context.isEng
//                   ? cityCost.city?.name ?? ''
//                   : cityLangToAr(cityCost.city?.name ?? ''),
//               style: context.headLine,
//             )),
//         Expanded(
//             child: Row(
//               children: [
//                 Expanded(
//                   child: BaseTextField(
//                     textInputType: TextInputType.number,
//                     initialValue: costValue.value,
//                     onChanged: (value) {
//                       if (value.isEmpty) return;
//                       shippingVM.onChangeCost(
//                         cityId: cityCost.city?.documentId ?? '0',
//                         cost: value,
//                       );
//                       costValue.value = value;
//                     },
//                   ),
//                 ),
//
//                 context.mediumGap,
//
//                 // ! Currency
//                 Text(
//                   vendorVM.currentCurrency(context),
//                   style: context.title,
//                 ),
//               ],
//             ))
//       ],
//     );
//   }
// }
//
// String cityLangToAr(String? enCityName) {
//   enCityName = enCityName?.toLowerCase();
//   switch (enCityName) {
//     case 'alexandria':
//       return 'الإسكندرية';
//     case 'aswan':
//       return 'أسوان';
//     case 'asyut':
//       return 'أسيوط';
//     case 'beheira':
//       return 'البحيرة';
//     case 'beni suef':
//       return 'بني سويف';
//     case 'cairo':
//       return 'القاهرة';
//     case 'dakahlia':
//       return 'الدقهلية';
//     case 'damietta':
//       return 'دمياط';
//     case 'fayoum':
//       return 'الفيوم';
//     case 'gharbia':
//       return 'الغربية';
//     case 'giza':
//       return 'الجيزة';
//     case 'ismailia':
//       return 'الإسماعيلية';
//     case 'kafr el-sheikh':
//       return 'كفر الشيخ';
//     case 'matrouh':
//       return 'مطروح';
//     case 'minya':
//       return 'المنيا';
//     case 'monufia':
//       return 'المنوفية';
//     case 'el wadi el gedid':
//       return 'الوادي الجديد';
//     case 'north sinai':
//       return 'شمال سيناء';
//     case 'port said':
//       return 'بورسعيد';
//     case 'qalyubia':
//       return 'القليوبية';
//     case 'qena':
//       return 'قنا';
//     case 'red sea':
//       return 'البحر الأحمر';
//     case 'sharqia':
//       return 'الشرقية';
//     case 'sohag':
//       return 'سوهاج';
//     case 'south sinai':
//       return 'جنوب سيناء';
//     case 'suez':
//       return 'السويس';
//     case 'luxor':
//       return 'الأقصر';
//     default:
//       return enCityName ?? 'Unknown';
//   }
// }
