import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/repository/shipping_repository.dart';

import '../model/shipping_model.dart';

class ShippingVM extends BaseVM {
  final ShippingRepository _shippingRepo;

  ShippingVM(this._shippingRepo);

  ShippingModel? vendorShipping;

  var citiesList = <CityCostModel>[];

  //! Get shipping ==================================
  Future<void> getShipping(
    BuildContext context,
  ) async {
    await baseFunction(context, () async {
      await getCities(context);

      vendorShipping = await _shippingRepo.getShipping();

      //! copy with vendor shipping with cities name and the real cost for each one
      citiesList = vendorShipping?.citiesCost ?? [];
      //   citiesList = citiesList
      //       .map((e) => e.copyWith(
      //             freeShipping: vendorShipping?.citiesCost
      //                     ?.firstWhereOrNull((element) =>
      //                         element.city?.documentId == e.city?.documentId)
      //                     ?.freeShipping ??
      //                 false,
      //             cost: vendorShipping?.citiesCost
      //                     ?.firstWhereOrNull((element) =>
      //                         element.city?.documentId == e.city?.documentId)
      //                     ?.cost ??
      //                 0,
      //             isActive: vendorShipping?.citiesCost
      //                     ?.firstWhereOrNull((element) =>
      //                         element.city?.documentId == e.city?.documentId)
      //                     ?.isActive ??
      //                 false,
      //           ))
      //       .toList();
    });
  }

  //! Get cities ==================================
  Future<List<CityCostModel>> getCities(
    BuildContext context,
  ) async {
    return await baseFunction(context, () async {
      citiesList = (await _shippingRepo.getCities()).toList();

      return citiesList;
    });
  }

  //! Edit Shipping ================================
  Future<void> editShipping(
      {required BuildContext context, bool isEdit = true}) async {
    await baseFunction(context, () async {
      Log.f('vendorShipping ${vendorShipping?.toCityCostJson()}');
      await _shippingRepo.editShipping(shippingModel: vendorShipping);

      if (!isEdit) {
        context.back();
      }

      context.showBarMessage(
        isEdit ? context.tr.editedSuccessfully : context.tr.addedSuccessfully,
      );
    });
  }

//! on change field cost copy with vendor shipping
  void onChangeCost({
    required String cityId,
    required String cost,
    required bool freeShipping,
    required bool isActive,
  }) {
    // check if the city not exist in the list add it
    if (vendorShipping?.citiesCost?.firstWhereOrNull(
            (element) => element.city?.documentId == cityId) ==
        null) {
      vendorShipping?.citiesCost?.add(
        CityCostModel(
          city: CityModel(
            documentId: cityId,
          ),
          cost: num.parse(cost),
          freeShipping: freeShipping,
          isActive: isActive,
        ),
      );
    }

    //? Update the cost for the city
    vendorShipping = vendorShipping?.copyWith(
      citiesCost: vendorShipping?.citiesCost
          ?.map((e) => e.copyWith(
                cost: e.city?.documentId == cityId ? num.parse(cost) : e.cost,
                freeShipping: e.city?.documentId == cityId
                    ? freeShipping
                    : e.freeShipping,
              ))
          .toList(),
    );
  }

  // ! Update city areas
  void updateCityAreas({
    required String cityId,
    required List<AreaModel> areas,
  }) {
    // Check if the city exists in the list
    final cityExists = vendorShipping?.citiesCost?.firstWhereOrNull(
          (element) => element.city?.documentId == cityId,
        ) !=
        null;

    // If the city doesn't exist and we have areas to add, create a new city cost model
    if (!cityExists && areas.isNotEmpty) {
      vendorShipping?.citiesCost?.add(
        CityCostModel(
          city: CityModel(
            documentId: cityId,
          ),
          areas: areas,
        ),
      );
    } else if (cityExists) {
      // Update the areas for the existing city
      vendorShipping = vendorShipping?.copyWith(
        citiesCost: vendorShipping?.citiesCost
            ?.map((e) =>
                e.city?.documentId == cityId ? e.copyWith(areas: areas) : e)
            .toList(),
      );
    }

    Log.i('Updated city areas: $cityId, areas count: ${areas.length}');
    notifyListeners();
  }
}
