import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';

// class ConfigModel {
//   final int? id;
//   final String? documentId;
//   final List<ExtraSettingsModel> sizes;
//   final List<ExtraSettingsModel>? colors;
//   final int bannerLimit;
//   final bool isActiveWorkingTime;
//   final num? minimumOrderCost;
//
//   const ConfigModel({
//     this.id,
//     this.documentId,
//     this.bannerLimit = 0,
//     this.sizes = const [],
//     this.colors = const [],
//     this.isActiveWorkingTime = true,
//     this.minimumOrderCost,
//   });
//
//   factory ConfigModel.fromJson(json) {
//     // * ===========================================
//
//     if (json == null || json.isEmpty) {
//       return const ConfigModel();
//     }
//
//     // final vendor = VendorModel.fromJson(json[ApiStrings.vendor]);
//
//     // * ===========================================
//
//     // final currencies = json[ApiStrings.currencies] as List;
//
//     // final currenciesList =
//     //     currencies.map((e) => CurrencyModel.fromJson(e)).toList();
//
//     // * ===========================================
//
//     final sizes = json[ApiStrings.sizes] as List;
//
//     final sizesList = sizes.map((e) => ExtraSettingsModel.fromJson(e)).toList();
//
//     // * ===========================================
//     final colors = json[ApiStrings.colors] as List;
//
//     final colorsList =
//         colors.map((e) => ExtraSettingsModel.fromJson(e)).toList();
//     //
//     //
//
//     return ConfigModel(
//         id: json[ApiStrings.id],
//         documentId: json[ApiStrings.documentId],
//         minimumOrderCost: json[ApiStrings.minimumOrderCost] ?? 0,
//         isActiveWorkingTime: json[ApiStrings.isActiveWorkingTime] ?? true,
//         sizes: sizesList,
//         colors: colorsList,
//         bannerLimit: json[ApiStrings.bannerLimit] ?? 0);
//   }
//
//   Map<String, dynamic> toJson() {
//     final sizesList = sizes.map((e) {
//       final categories = <int>[];
//       for (int i = 0; i < e.categories.length; i++) {
//         categories.add(e.categories[i].id!);
//       }
//       return {
//         ApiStrings.name: e.englishName,
//         ApiStrings.arabicName: e.arabicName,
//         ApiStrings.categories: categories,
//       };
//     }).toList();
//
//     return {
//       if (documentId != null) ApiStrings.documentId: documentId,
//       if (sizes.isNotEmpty) ApiStrings.sizes: sizesList,
//       if (colors != null)
//         ApiStrings.colors: colors?.map((e) => e.toJson()).toList(),
//       ApiStrings.isActiveWorkingTime: isActiveWorkingTime,
//       if (minimumOrderCost != null)
//         ApiStrings.minimumOrderCost: minimumOrderCost,
//     };
//   }
//
//   //! Copy With
//   ConfigModel copyWith({
//     int? id,
//     String? documentId,
//     List<ExtraSettingsModel>? sizes,
//     List<ExtraSettingsModel>? colors,
//     List<BannerModel>? banners,
//     bool isActiveWorkingTime = true,
//     num? minimumOrderCost,
//   }) {
//     return ConfigModel(
//       id: id ?? this.id,
//       documentId: documentId ?? this.documentId,
//       // currencies: currencies ?? this.currencies,
//       sizes: sizes ?? this.sizes,
//       colors: colors ?? this.colors,
//       minimumOrderCost: minimumOrderCost ?? minimumOrderCost,
//       isActiveWorkingTime: isActiveWorkingTime,
//     );
//   }
// }

class ExtraSettingsModel {
  final int? id;
  final String? englishName;
  final String? arabicName;
  int? stock;
  num? price;
  final List<CategoryModel> categories;

  ExtraSettingsModel({
    this.id,
    this.englishName = '',
    this.arabicName = '',
    this.stock,
    this.price,
    this.categories = const [],
  });

  // name by lang
  //  String nameByLang(BuildContext context) {
  //     final isEnglish = context.readIsEng;
  //
  //     return isEnglish
  //         ? (englishTitle?.isNotEmpty == true ? englishTitle! : arabicTitle ?? '')
  //         : (arabicTitle?.isNotEmpty == true ? arabicTitle! : englishTitle ?? '');
  //   }
  String nameByLang(BuildContext context) {
    final isEnglish = context.readIsEng;

    return isEnglish
        ? (englishName?.isNotEmpty == true ? englishName! : arabicName ?? '')
        : (arabicName?.isNotEmpty == true ? arabicName! : englishName ?? '');
  }

  factory ExtraSettingsModel.fromJson(Map<String, dynamic> json) {
    log('ExtraSettingsModel.fromJson: $json');
    final categories = json[ApiStrings.categories] as List;

    final categoriesList =
        categories.map((e) => CategoryModel.fromJson(e)).toList();

    return ExtraSettingsModel(
      id: json[ApiStrings.id],
      englishName: json[ApiStrings.name] ?? '',
      arabicName: json[ApiStrings.arabicName] ?? '',
      stock: int.tryParse(json[ApiStrings.stock].toString()) ?? 0,
      price: json[ApiStrings.price],
      categories: categoriesList,
    );
  }

  factory ExtraSettingsModel.fromProductJson(Map<String, dynamic> json) {
    return ExtraSettingsModel(
      englishName: json[ApiStrings.name] ?? '',
      stock: int.tryParse(json[ApiStrings.stock].toString()) ?? 0,
      price: json[ApiStrings.price],
    );
  }

  // copyWith
  ExtraSettingsModel copyWith({
    int? id,
    String? name,
    String? arabicName,
    String? englishName,
    int? stock,
    num? price,
    List<CategoryModel>? categories,
    bool canStockBeNull = false,
  }) {
    return ExtraSettingsModel(
      id: id ?? this.id,
      englishName: name ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      stock: canStockBeNull ? null : stock ?? this.stock,
      price: price ?? this.price,
      categories: categories ?? this.categories,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (englishName != null) ApiStrings.name: englishName,
      if (arabicName != null) ApiStrings.arabicName: arabicName,
      ApiStrings.categories: categories.map((e) => e.id).toList(),
      ApiStrings.vendor: VendorModelHelper.currentVendorId(),
      ApiStrings.price: price,
    };
  }

  //? To Product Json
  Map<String, dynamic> toProductJson() {
    return {
      ApiStrings.name: englishName,
      ApiStrings.arabicName: arabicName,
      if (stock != null) ApiStrings.stock: stock?.toString(),
      ApiStrings.price: price,
    };
  }

  @override
  String toString() {
    return toJson().toString();
  }
}
