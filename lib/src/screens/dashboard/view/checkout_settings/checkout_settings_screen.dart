import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/checkout_settings_model.dart';

import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/drawer/widgets/settings_widgets.dart';
import 'package:provider/provider.dart';

import '../../../../core/shared_widgets/appbar/main_appbar.dart';

class CheckoutSettingsScreen extends HookWidget {
  const CheckoutSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();
    final config = VendorModelHelper.currentVendorModel().config;
    final checkoutSettings =
        config?.checkoutSettings ?? const CheckoutSettingsModel();

    final checkoutType = useState(checkoutSettings.checkoutType);
    final showCheckout = useState(checkoutSettings.showCheckout);
    final guestCheckout = useState(checkoutSettings.guestCheckout);

    final emailShow = useState(checkoutSettings.email.show);
    final emailRequired = useState(checkoutSettings.email.isRequired);

    final nameShow = useState(checkoutSettings.name.show);
    final nameRequired = useState(checkoutSettings.name.isRequired);

    final phoneShow = useState(checkoutSettings.phone.show);
    final phoneRequired = useState(checkoutSettings.phone.isRequired);

    void saveSettings() {
      final updatedCheckoutSettings = CheckoutSettingsModel(
        id: checkoutSettings.id,
        checkoutType: checkoutType.value,
        showCheckout: showCheckout.value,
        guestCheckout: guestCheckout.value,
        email: checkoutSettings.email.copyWith(
          show: emailShow.value,
          isRequired: emailRequired.value,
        ),
        name: checkoutSettings.name.copyWith(
          show: nameShow.value,
          isRequired: nameRequired.value,
        ),
        phone: checkoutSettings.phone.copyWith(
          show: phoneShow.value,
          isRequired: phoneRequired.value,
        ),
      );

      final updatedConfig = config?.copyWith(
        checkoutSettings: updatedCheckoutSettings,
      );

      if (updatedConfig == null) {
        return;
      }

      authVM.updateVendorConfig(
        context,
        config: updatedConfig!,
      );
    }

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.checkout_settings,
        haveBackButton: true,
        actionWidget: Button(
          label: context.tr.save,
          onPressed: saveSettings,
          isLoading: authVM.isLoading,
        ).sized(height: 40, width: 80),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Checkout Type Section
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: context.appTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.checkout_type,
                    style: context.title.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  context.mediumGap,
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<CheckoutType>(
                          title: Text(context.tr.steps_checkout),
                          value: CheckoutType.steps,
                          groupValue: checkoutType.value,
                          onChanged: (value) {
                            if (value != null) checkoutType.value = value;
                          },
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<CheckoutType>(
                          title: Text(context.tr.one_page_checkout),
                          value: CheckoutType.onePage,
                          groupValue: checkoutType.value,
                          onChanged: (value) {
                            if (value != null) checkoutType.value = value;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            context.largeGap,

            // General Settings Section
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: context.appTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.general_settings,
                    style: context.title.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  context.mediumGap,
                  SettingsWidget(
                    header: context.tr.show_checkout,
                    trailingWidget: SwitchButtonWidget(
                      value: showCheckout,
                      onChanged: (value) => showCheckout.value = value,
                    ),
                    onTap: () => showCheckout.value = !showCheckout.value,
                  ),
                  SettingsWidget(
                    header: context.tr.guest_checkout,
                    trailingWidget: SwitchButtonWidget(
                      value: guestCheckout,
                      onChanged: (value) => guestCheckout.value = value,
                    ),
                    onTap: () => guestCheckout.value = !guestCheckout.value,
                  ),
                ],
              ),
            ),

            context.largeGap,

            // Field Settings Section
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: context.appTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.field_settings,
                    style: context.title.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  context.mediumGap,

                  // Email Field
                  _buildFieldSettings(
                    context,
                    title: context.tr.email_field,
                    show: emailShow,
                    required: emailRequired,
                  ),

                  context.mediumGap,

                  // Name Field
                  _buildFieldSettings(
                    context,
                    title: context.tr.name_field,
                    show: nameShow,
                    required: nameRequired,
                  ),

                  context.mediumGap,

                  // Phone Field
                  _buildFieldSettings(
                    context,
                    title: context.tr.phone_field,
                    show: phoneShow,
                    required: phoneRequired,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFieldSettings(
    BuildContext context, {
    required String title,
    required ValueNotifier<bool> show,
    required ValueNotifier<bool> required,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: context.subTitle.copyWith(fontWeight: FontWeight.w600),
          ),
          context.smallGap,
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(context.tr.show_field),
                    const Spacer(),
                    SwitchButtonWidget(
                      value: show,
                      onChanged: (value) => show.value = value,
                    ),
                  ],
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Row(
                  children: [
                    Text(context.tr.required_field),
                    const Spacer(),
                    SwitchButtonWidget(
                      value: required,
                      onChanged: (value) => required.value = value,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
