import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/checkout_settings/checkout_settings_screen.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/size_and_colors/size_and_color_screen.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/social_links_screen.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/widgets/global_sale_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/widgets/minimum_order_cost_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/payment/view/payment_screen.dart';
import 'package:provider/provider.dart';

import '../../../../generated/assets.dart';
import '../../../core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import '../../application_and_website_settings/primary_color/view/primary_color_screen.dart';
import '../../application_and_website_settings/widgets/default_language_dialog_widget.dart';
import '../../application_and_website_settings/widgets/default_theme_dialog_widget.dart';
import '../../auth/models/helper_models/config_model.dart';
import '../../auth/view_model/auth_view_model.dart';
import '../../banner/view/banner_screen.dart';
import '../../drawer/widgets/settings_widgets.dart';
import '../../employee/view/employees_screen.dart';
import '../../expenses/view/expenses_screen.dart';
import '../../promo_codes/view/promo_codes_screen.dart';
import '../../qr_landing/view/qr_landing_screen.dart';
import '../../reports/reports_screen.dart';
import '../../shipping/view/shipping_screen.dart';
import '../../tasks/view/tasks_screen.dart';
import '../../users/view/users_orders_screen.dart';
import '../../users/view/users_screen.dart';

class DashboardScreen extends HookWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final vendorName = VendorModelHelper.currentVendorModel().name ?? '';

    final authVM = context.read<AuthVM>();

    final config = VendorModelHelper.currentVendorModel().config;

    final isNewOrderBellEnabled = useState(config?.orderRingingBell ?? false);

    void onNewAlertTapped() {
      isNewOrderBellEnabled.value = !isNewOrderBellEnabled.value;

      authVM.updateVendorConfig(
        config: config?.copyWith(
              orderRingingBell: isNewOrderBellEnabled.value,
            ) ??
            ConfigModel(
              orderRingingBell: isNewOrderBellEnabled.value,
            ),
        context,
      );

      context.showBarMessage(
        context.tr.youMayNeedToReInstallTheAppToApplyChanges,
        duration: 5,
      );
    }

    return ListView(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.xlLargePadding + 10),
      children: [
        // * Employees
        if (VendorModelHelper.canManageEmployees()) ...[
          ElevatedButton(
            onPressed: () {
              context.to(EmployeesScreen());
            },
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppRadius.baseRadius),
              ),
              backgroundColor: Colors.transparent,
            ),
            child: Ink(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: ColorManager.activeGradientBackground,
                ),
                borderRadius: BorderRadius.circular(AppRadius.baseRadius),
              ),
              child: Container(
                padding:
                    EdgeInsets.symmetric(horizontal: AppSpaces.smallPadding),
                child: Row(
                  children: [
                    Image.asset(
                      Assets.assetsImagesEmployee,
                      width: 80,
                    ),
                    context.mediumGap,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr.addAndManageEmployees,
                            style: context.whiteTitle,
                          ),
                          context.xSmallGap,
                          Text(
                            context.tr.inviteEmployeesToYourStore,
                            style: context.whiteHint,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          context.mediumGap,
        ],
        //* ⚙️ App & Website Settings
        if (VendorModelHelper.canAccessAppSettings()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.appAndWebsiteSettings,
            children: [
              SettingsWidget(
                onTap: () => context.to(const PrimaryColorScreen()),
                header: context.tr.primaryColor,
                iconPath: Assets.primarySvg,
              ),
              SettingsWidget(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => const DefaultLanguageDialogWidget(),
                  );
                },
                header: context.tr.defaultLanguage,
                iconPath: Assets.iconsLang,
              ),
              SettingsWidget(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => const DefaultThemeDialogWidget(),
                  );
                },
                header: context.tr.defaultTheme,
                iconPath: context.isDark ? Assets.iconsMoon : Assets.iconsSun,
              ),
              SettingsWidget(
                onTap: () => context.to(const QrLandingScreen()),
                header: context.tr.qrLanding,
                iconPath: Assets.qrCode,
              ),
              SettingsWidget(
                onTap: () => context.to(const SocialLinksScreen()),
                header: context.tr.socialLinks,
                iconPath: Assets.iconsLinks,
              ),
              SettingsWidget(
                onTap: () => context.to(const CheckoutSettingsScreen()),
                header: context.tr.checkout_settings,
                iconPath: Assets.iconsPayment,
              ),
            ],
          ),
          context.mediumGap,
        ],

        //* 🛍️ Orders & Pricing
        if (VendorModelHelper.canAccessOrdersShipping()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.ordersAndShipping,
            children: [
              SettingsWidget(
                verticalPadding: AppSpaces.smallPadding,
                trailingWidget: SwitchButtonWidget(
                  value: isNewOrderBellEnabled,
                  onChanged: (_) => onNewAlertTapped(),
                ),
                onTap: onNewAlertTapped,
                header: context.tr.newOrdersAlertBell,
                iconPath: Assets.iconsNotifications,
              ),
              SettingsWidget(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => const MinimumOrderCostDialog(),
                  );
                },
                header: context.tr.minimumOrderCost,
                iconPath: Assets.iconsShippingCost,
              ),
              SettingsWidget(
                onTap: () => context.to(const ShippingScreen()),
                header: context.tr.shippingCost,
                iconPath: Assets.iconsShippingCost,
              ),
            ],
          ),
          context.mediumGap,
        ],

        //* 🎨 Store Appearance
        if (VendorModelHelper.canAccessStoreAppearance()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.storeAppearance,
            children: [
              SettingsWidget(
                onTap: () => context.to(const BannerScreen()),
                header: context.tr.banners,
                iconPath: Assets.iconsAds,
              ),
              SettingsWidget(
                onTap: () => context.to(const SizeAndColorScreen()),
                header: context.tr.sizesAndColors,
                iconPath: Assets.iconsColorsAndSizes,
              ),
            ],
          ),
          context.mediumGap,
        ],

        //* 💳 Payments & Promotions
        if (VendorModelHelper.canAccessPaymentsPromotions()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.paymentsAndPromotions,
            children: [
              SettingsWidget(
                onTap: () => context.to(const PaymentScreen()),
                header: context.tr.paymentMethods,
                iconPath: Assets.iconsPayment,
              ),
              SettingsWidget(
                onTap: () => showDialog(
                  context: context,
                  builder: (_) => const GlobalSaleDialog(),
                ),
                header: context.tr.globalOffer,
                iconPath: Assets.iconsFire,
              ),
              SettingsWidget(
                onTap: () => context.to(const PromoCodesScreen()),
                header: context.tr.promoCodes,
                iconPath: Assets.iconsDiscount,
              ),
            ],
          ),
          context.mediumGap,
        ],

        //* 👥 Users
        if (VendorModelHelper.canAccessUsers()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.users,
            children: [
              SettingsWidget(
                onTap: () => context.to(const UsersScreen()),
                header: context.tr.users,
                iconPath: Assets.iconsUserCircle,
              ),
              SettingsWidget(
                onTap: () => context.to(const UsersOrdersScreen()),
                header: context.tr.usersOrders,
                iconPath: Assets.ordersSvg,
              ),
            ],
          ),
          context.mediumGap,
        ],

        //* 📊 Reports & Operations
        if (VendorModelHelper.canAccessReportsOperations()) ...[
          DashboardExpansionTileWidget(
            title: context.tr.reportsAndOperations,
            children: [
              SettingsWidget(
                onTap: () => context.to(const ReportsScreen()),
                header: context.tr.reports,
                iconPath: Assets.iconsAnalytics,
              ),
              SettingsWidget(
                onTap: () => context.to(const ExpensesScreen()),
                header: context.tr.expenses,
                iconPath: Assets.iconsMoney,
              ),
              SettingsWidget(
                onTap: () => context.to(const TasksScreen()),
                header: context.tr.tasks,
                iconPath: Assets.iconsTasks,
              ),
            ],
          ),
        ],
      ],
    );
  }
}

class DashboardExpansionTileWidget extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const DashboardExpansionTileWidget(
      {super.key, required this.title, required this.children});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: ColorManager.textFieldColor(context),
          borderRadius: BorderRadius.circular(AppRadius.baseRadius)),
      child: ExpansionTile(
        iconColor: context.appTheme.primaryColorDark,
        shape: RoundedRectangleBorder(side: BorderSide.none),
        title: Text(title, style: context.labelLarge),
        children: children,
      ),
    );
  }
}
