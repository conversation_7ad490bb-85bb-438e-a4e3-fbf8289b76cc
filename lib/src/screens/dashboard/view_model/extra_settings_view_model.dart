import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';

import '../../auth/models/helper_models/config_model.dart';
import '../../shipping/model/shipping_model.dart';
import '../repository/extra_settings_repository.dart';

class ExtraSettingVM extends BaseVM {
  final ExtraSettingsRepository extraSettingRepository;

  ExtraSettingVM(this.extraSettingRepository);

  ConfigModel? extraSettings;

  var allCategories = <CategoryModel>[];

  var shipping = const ShippingModel();

  List<ExtraSettingsModel> filterCategoriesSizes({required int index}) {
    final categorySizes = extraSettings!.sizes
        .where((element) => element.categories
            .map((e) => e.documentId)
            .contains(allCategories[index].documentId))
        .toList();

    return categorySizes;
  }

  //! Get extraSettings ====================================
  Future<void> getExtraSettings(BuildContext context) async {
    await baseFunction(context, () async {
      allCategories.clear();

      extraSettings = await extraSettingRepository.getExtraSettings();

      //! Add Sizes To Categories
      for (int i = 0; i < extraSettings!.sizes.length; i++) {
        getSizesPerCategory(size: extraSettings!.sizes[i]);
      }

      //? sort categories by id
      allCategories.sort((a, b) => a.documentId!.compareTo(b.documentId!));
    });
  }

  void getSizesPerCategory({required ExtraSettingsModel size}) {
    for (int j = 0; j < size.categories.length; j++) {
      final checkIfExistAdded = !allCategories
          .map((e) => e.documentId)
          .contains(size.categories[j].documentId);

      //? check if already exist in allSizesCategories
      if (checkIfExistAdded) {
        allCategories.add(size.categories[j]);
      }
    }
  }

  //! Add extraSetting ====================================
  Future<void> addExtraSetting(BuildContext context,
      {required ConfigModel extraSettings, bool isBack = false}) async {
    await baseFunction(context, () async {
      if (isBack) {
        context.back();
      }
      await extraSettingRepository.updateExtraSetting(
          extraSettings: extraSettings);

      await getExtraSettings(context);
    }, type: FlushBarType.add, isBack: false);
  }

//! Update extraSetting ====================================
  Future<void> updateExtraSetting(BuildContext context,
      {required ExtraSettingsModel extraSetting}) async {
    await baseFunction(context, () async {
      context.back();

      final copiedWithExtraSettings = extraSettings!.copyWith(
        sizes: extraSettings!.sizes
            .map((e) => e.id == extraSetting.id ? extraSetting : e)
            .toList(),
      );

      await extraSettingRepository.updateExtraSetting(
          extraSettings: copiedWithExtraSettings);

      if (!context.mounted) return;
      await getExtraSettings(context);
    }, type: FlushBarType.delete);
  }

  //! Delete extraSetting ====================================
  Future<void> deleteExtraSetting(BuildContext context,
      {required ExtraSettingsModel extraSetting,
      required CategoryModel cat}) async {
    await baseFunction(
      context,
      () async {
        context.back();

        final sizeCategoriesWithoutDeleted = extraSettings!.sizes
            .map((e) => e.id == extraSetting.id
                ? e.copyWith(
                    categories: e.categories
                        .where((element) => element.id != cat.id)
                        .toList())
                : e)
            .toList();

        final copiedWithExtraSettings = extraSettings!.copyWith(
          sizes: sizeCategoriesWithoutDeleted,
        );

        await extraSettingRepository.updateExtraSetting(
            extraSettings: copiedWithExtraSettings);

        if (!context.mounted) return;
        await getExtraSettings(context);
      },
      type: FlushBarType.delete,
    );
  }

  //! Delete Color ====================================
  Future<void> deleteColor(BuildContext context,
      {required ExtraSettingsModel color}) async {
    await baseFunction(context, () async {
      context.back();

      final colorsWithoutDeleted = extraSettings!.colors;

      colorsWithoutDeleted?.removeWhere((element) => element.id == color.id);

      final copiedWithExtraSettings = extraSettings!.copyWith(
        colors: colorsWithoutDeleted,
      );

      Log.w(
          'deleteColor: copiedWithExtraSettings: ${copiedWithExtraSettings.toJson()}');

      await extraSettingRepository.updateExtraSetting(
          extraSettings: copiedWithExtraSettings);
    }, type: FlushBarType.delete, additionalFunction: getExtraSettings);
  }

  void selectOrUnSelectColor({
    required ValueNotifier<List<ExtraSettingsModel>> selectedColors,
    required String color,
    bool isSingle = false,
  }) {
    final colorIsSelected =
        selectedColors.value.any((e) => e.englishName == color);

    if (colorIsSelected) {
      selectedColors.value.removeWhere((e) => e.englishName == color);
    } else {
      if (isSingle) {
        selectedColors.value.clear();
      }
      selectedColors.value.add(ExtraSettingsModel(englishName: color));
    }

    log('asfsafsa $color');

    notifyListeners();
  }

  void selectOrUnSelectSize({
    required ValueNotifier<List<ExtraSettingsModel>> selectedSizes,
    required ExtraSettingsModel size,
    bool isSingle = false,
  }) {
    final sizeIsSelected =
        selectedSizes.value.any((e) => e.englishName == size.englishName);

    if (sizeIsSelected && !isSingle) {
      selectedSizes.value.removeWhere((e) => e.englishName == size.englishName);
    } else {
      if (isSingle) {
        selectedSizes.value.clear();
      }

      selectedSizes.value.add(size);
    }

    notifyListeners();
  }

//   final sizeIsSelected = selectedSizes.value.contains(size);
//
//   if (sizeIsSelected) {
//   selectedSizes.value.remove(size);
//   } else {
//   if (isSingle && selectedSizes.value.isNotEmpty) {
//   selectedSizes.value.clear();
//   }
//   selectedSizes.value.add(size);
//   }
//   notifyListeners();
// }
}
