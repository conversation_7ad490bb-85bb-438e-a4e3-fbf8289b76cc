import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/application_and_website_settings/primary_color/view/primary_color_screen.dart';
import 'package:idea2app_vendor_app/src/screens/application_and_website_settings/widgets/default_language_dialog_widget.dart';
import 'package:provider/provider.dart';

import '../../../generated/assets.dart';
import '../../core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import '../auth/models/helper_models/config_model.dart';
import '../auth/models/helper_models/vendor_helper_model.dart';
import '../auth/view_model/auth_view_model.dart';
import '../dashboard/view/widgets/minimum_order_cost_dialog.dart';
import '../drawer/widgets/settings_widgets.dart';
import '../shipping/view/shipping_screen.dart';

class ApplicationAndWebsiteSettingsScreen extends HookWidget {
  const ApplicationAndWebsiteSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final config = VendorModelHelper.currentVendorModel().config;

    final isNewOrderBellEnabled = useState(config?.orderRingingBell ?? false);

    void onNewAlertTapped() {
      isNewOrderBellEnabled.value = !isNewOrderBellEnabled.value;

      authVM.updateVendorConfig(
        config: config?.copyWith(
              orderRingingBell: isNewOrderBellEnabled.value,
            ) ??
            ConfigModel(
              orderRingingBell: isNewOrderBellEnabled.value,
            ),
        context,
      );

      context.showBarMessage(
        context.tr.youMayNeedToReInstallTheAppToApplyChanges,
        duration: 5,
      );
    }

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.applicationAndWebsiteSettings,
        haveBackButton: true,
      ),
      body: ListView(
        padding: EdgeInsets.all(AppSpaces.smallPadding),
        children: [
          SettingsWidget(
            onTap: () => context.to(const PrimaryColorScreen()),
            header: context.tr.primaryColor,
            iconPath: Assets.primarySvg,
          ),

          //  ! Default Lang ------------------------------
          SettingsWidget(
            onTap: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return const DefaultLanguageDialogWidget();
                },
              );
            },
            header: context.tr.defaultLanguage,
            iconPath: Assets.iconsLang,
          ),
          SettingsWidget(
            verticalPadding: AppSpaces.smallPadding,
            trailingWidget: SwitchButtonWidget(
              value: isNewOrderBellEnabled,
              onChanged: (value) {
                onNewAlertTapped();
              },
            ),
            onTap: onNewAlertTapped,
            header: context.tr.newOrdersAlertBell,
            iconPath: Assets.iconsNotifications,
          ),

          //! Shipping ------------------------------
          SettingsWidget(
            onTap: () => context.to(const ShippingScreen()),
            header: context.tr.shippingCost,
            iconPath: Assets.iconsShippingCost,
          ),

          //! Minimum Order Cost ------------------------------
          SettingsWidget(
            onTap: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return const MinimumOrderCostDialog();
                },
              );
            },
            header: context.tr.minimumOrderCost,
            iconPath: Assets.iconsShippingCost,
          ),
        ],
      ),
    );
  }
}
