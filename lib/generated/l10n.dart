// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `About`
  String get about {
    return Intl.message('About', name: 'about', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Verify`
  String get verify {
    return Intl.message('Verify', name: 'verify', desc: '', args: []);
  }

  /// `Select your preferred languages`
  String get select_your_preferred_languages {
    return Intl.message(
      'Select your preferred languages',
      name: 'select_your_preferred_languages',
      desc: '',
      args: [],
    );
  }

  /// `Order Id`
  String get order_id {
    return Intl.message('Order Id', name: 'order_id', desc: '', args: []);
  }

  /// `Category`
  String get category {
    return Intl.message('Category', name: 'category', desc: '', args: []);
  }

  /// `Checkout`
  String get checkout {
    return Intl.message('Checkout', name: 'checkout', desc: '', args: []);
  }

  /// `Payment Mode`
  String get payment_mode {
    return Intl.message(
      'Payment Mode',
      name: 'payment_mode',
      desc: '',
      args: [],
    );
  }

  /// `As a tech company, we specialize in software solutions, offering a diverse range of products and expertise in developing bespoke applications. Our team is dedicated to crafting innovative software products, and we have the capability to create tailored software apps designed to meet your specific requirements. In addition, we offer a cutting-edge vendor app that empowers you to efficiently manage your business. We are committed to enhancing your business through our advanced technology solutions.`
  String get aboutDescription {
    return Intl.message(
      'As a tech company, we specialize in software solutions, offering a diverse range of products and expertise in developing bespoke applications. Our team is dedicated to crafting innovative software products, and we have the capability to create tailored software apps designed to meet your specific requirements. In addition, we offer a cutting-edge vendor app that empowers you to efficiently manage your business. We are committed to enhancing your business through our advanced technology solutions.',
      name: 'aboutDescription',
      desc: '',
      args: [],
    );
  }

  /// `Subtotal`
  String get subtotal {
    return Intl.message('Subtotal', name: 'subtotal', desc: '', args: []);
  }

  /// `Total`
  String get total {
    return Intl.message('Total', name: 'total', desc: '', args: []);
  }

  /// `Total Price:`
  String get totalPrice {
    return Intl.message('Total Price:', name: 'totalPrice', desc: '', args: []);
  }

  /// `Favorite Products`
  String get favorite_products {
    return Intl.message(
      'Favorite Products',
      name: 'favorite_products',
      desc: '',
      args: [],
    );
  }

  /// `Extras`
  String get extras {
    return Intl.message('Extras', name: 'extras', desc: '', args: []);
  }

  /// `Faq`
  String get faq {
    return Intl.message('Faq', name: 'faq', desc: '', args: []);
  }

  /// `Help & Supports`
  String get help_supports {
    return Intl.message(
      'Help & Supports',
      name: 'help_supports',
      desc: '',
      args: [],
    );
  }

  /// `App Language`
  String get app_language {
    return Intl.message(
      'App Language',
      name: 'app_language',
      desc: '',
      args: [],
    );
  }

  /// `I forgot password ?`
  String get i_forgot_password {
    return Intl.message(
      'I forgot password ?',
      name: 'i_forgot_password',
      desc: '',
      args: [],
    );
  }

  /// `I don't have an account?`
  String get i_dont_have_an_account {
    return Intl.message(
      'I don\'t have an account?',
      name: 'i_dont_have_an_account',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `WhatsApp`
  String get whatsApp {
    return Intl.message('WhatsApp', name: 'whatsApp', desc: '', args: []);
  }

  /// `YouTube`
  String get youtube {
    return Intl.message('YouTube', name: 'youtube', desc: '', args: []);
  }

  /// `TAX`
  String get tax {
    return Intl.message('TAX', name: 'tax', desc: '', args: []);
  }

  /// `Edit profile`
  String get editProfile {
    return Intl.message(
      'Edit profile',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Cash on delivery`
  String get cash_on_delivery {
    return Intl.message(
      'Cash on delivery',
      name: 'cash_on_delivery',
      desc: '',
      args: [],
    );
  }

  /// `Recent Orders`
  String get recent_orders {
    return Intl.message(
      'Recent Orders',
      name: 'recent_orders',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Profile Settings`
  String get profile_settings {
    return Intl.message(
      'Profile Settings',
      name: 'profile_settings',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get full_name {
    return Intl.message('Full name', name: 'full_name', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Phone`
  String get phone {
    return Intl.message('Phone', name: 'phone', desc: '', args: []);
  }

  /// `Address`
  String get address {
    return Intl.message('Address', name: 'address', desc: '', args: []);
  }

  /// `App Settings`
  String get app_settings {
    return Intl.message(
      'App Settings',
      name: 'app_settings',
      desc: '',
      args: [],
    );
  }

  /// `Languages`
  String get languages {
    return Intl.message('Languages', name: 'languages', desc: '', args: []);
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `Help & Support`
  String get help_support {
    return Intl.message(
      'Help & Support',
      name: 'help_support',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message('Register', name: 'register', desc: '', args: []);
  }

  /// `Let's Start with register!`
  String get lets_start_with_register {
    return Intl.message(
      'Let\'s Start with register!',
      name: 'lets_start_with_register',
      desc: '',
      args: [],
    );
  }

  /// `Should be more than 3 letters`
  String get should_be_more_than_3_letters {
    return Intl.message(
      'Should be more than 3 letters',
      name: 'should_be_more_than_3_letters',
      desc: '',
      args: [],
    );
  }

  /// `John Doe`
  String get john_doe {
    return Intl.message('John Doe', name: 'john_doe', desc: '', args: []);
  }

  /// `Should be a valid email`
  String get should_be_a_valid_email {
    return Intl.message(
      'Should be a valid email',
      name: 'should_be_a_valid_email',
      desc: '',
      args: [],
    );
  }

  /// `Should be more than 6 letters`
  String get should_be_more_than_6_letters {
    return Intl.message(
      'Should be more than 6 letters',
      name: 'should_be_more_than_6_letters',
      desc: '',
      args: [],
    );
  }

  /// `Let's Login to your\naccount first!`
  String get letsLoginToYourAccountFirst {
    return Intl.message(
      'Let\'s Login to your\naccount first!',
      name: 'letsLoginToYourAccountFirst',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `I have an account ? `
  String get iHaveAnAccount {
    return Intl.message(
      'I have an account ? ',
      name: 'iHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Tracking Order`
  String get tracking_order {
    return Intl.message(
      'Tracking Order',
      name: 'tracking_order',
      desc: '',
      args: [],
    );
  }

  /// `Discover & Explorer`
  String get discover__explorer {
    return Intl.message(
      'Discover & Explorer',
      name: 'discover__explorer',
      desc: '',
      args: [],
    );
  }

  /// `Reset Cart?`
  String get reset_cart {
    return Intl.message('Reset Cart?', name: 'reset_cart', desc: '', args: []);
  }

  /// `Cart`
  String get cart {
    return Intl.message('Cart', name: 'cart', desc: '', args: []);
  }

  /// `Shopping Cart`
  String get shopping_cart {
    return Intl.message(
      'Shopping Cart',
      name: 'shopping_cart',
      desc: '',
      args: [],
    );
  }

  /// `Verify your quantity and click checkout`
  String get verify_your_quantity_and_click_checkout {
    return Intl.message(
      'Verify your quantity and click checkout',
      name: 'verify_your_quantity_and_click_checkout',
      desc: '',
      args: [],
    );
  }

  /// `Let's Start with Login!`
  String get lets_start_with_login {
    return Intl.message(
      'Let\'s Start with Login!',
      name: 'lets_start_with_login',
      desc: '',
      args: [],
    );
  }

  /// `Should be more than 3 characters`
  String get should_be_more_than_3_characters {
    return Intl.message(
      'Should be more than 3 characters',
      name: 'should_be_more_than_3_characters',
      desc: '',
      args: [],
    );
  }

  /// `Checkout Settings`
  String get checkout_settings {
    return Intl.message(
      'Checkout Settings',
      name: 'checkout_settings',
      desc: '',
      args: [],
    );
  }

  /// `Checkout Type`
  String get checkout_type {
    return Intl.message(
      'Checkout Type',
      name: 'checkout_type',
      desc: '',
      args: [],
    );
  }

  /// `Steps Checkout`
  String get steps_checkout {
    return Intl.message(
      'Steps Checkout',
      name: 'steps_checkout',
      desc: '',
      args: [],
    );
  }

  /// `One Page Checkout`
  String get one_page_checkout {
    return Intl.message(
      'One Page Checkout',
      name: 'one_page_checkout',
      desc: '',
      args: [],
    );
  }

  /// `Show Checkout`
  String get show_checkout {
    return Intl.message(
      'Show Checkout',
      name: 'show_checkout',
      desc: '',
      args: [],
    );
  }

  /// `Guest Checkout`
  String get guest_checkout {
    return Intl.message(
      'Guest Checkout',
      name: 'guest_checkout',
      desc: '',
      args: [],
    );
  }

  /// `Email Field`
  String get email_field {
    return Intl.message('Email Field', name: 'email_field', desc: '', args: []);
  }

  /// `Name Field`
  String get name_field {
    return Intl.message('Name Field', name: 'name_field', desc: '', args: []);
  }

  /// `Phone Field`
  String get phone_field {
    return Intl.message('Phone Field', name: 'phone_field', desc: '', args: []);
  }

  /// `Show Field`
  String get show_field {
    return Intl.message('Show Field', name: 'show_field', desc: '', args: []);
  }

  /// `Required Field`
  String get required_field {
    return Intl.message(
      'Required Field',
      name: 'required_field',
      desc: '',
      args: [],
    );
  }

  /// `Field Settings`
  String get field_settings {
    return Intl.message(
      'Field Settings',
      name: 'field_settings',
      desc: '',
      args: [],
    );
  }

  /// `You must add products of the same markets choose one markets only!`
  String get you_must_add_products_of_the_same_markets_choose_one {
    return Intl.message(
      'You must add products of the same markets choose one markets only!',
      name: 'you_must_add_products_of_the_same_markets_choose_one',
      desc: '',
      args: [],
    );
  }

  /// `Reset your cart and order meals form this market`
  String get reset_your_cart_and_order_meals_form_this_market {
    return Intl.message(
      'Reset your cart and order meals form this market',
      name: 'reset_your_cart_and_order_meals_form_this_market',
      desc: '',
      args: [],
    );
  }

  /// `Keep your old meals of this market`
  String get keep_your_old_meals_of_this_market {
    return Intl.message(
      'Keep your old meals of this market',
      name: 'keep_your_old_meals_of_this_market',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get reset {
    return Intl.message('Reset', name: 'reset', desc: '', args: []);
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Application Preferences`
  String get application_preferences {
    return Intl.message(
      'Application Preferences',
      name: 'application_preferences',
      desc: '',
      args: [],
    );
  }

  /// `Help & Support`
  String get help__support {
    return Intl.message(
      'Help & Support',
      name: 'help__support',
      desc: '',
      args: [],
    );
  }

  /// `Light Mode`
  String get light_mode {
    return Intl.message('Light Mode', name: 'light_mode', desc: '', args: []);
  }

  /// `Dark Mode`
  String get dark_mode {
    return Intl.message('Dark Mode', name: 'dark_mode', desc: '', args: []);
  }

  /// `Log out`
  String get log_out {
    return Intl.message('Log out', name: 'log_out', desc: '', args: []);
  }

  /// `Version`
  String get version {
    return Intl.message('Version', name: 'version', desc: '', args: []);
  }

  /// `D'ont have any item in your cart`
  String get dont_have_any_item_in_your_cart {
    return Intl.message(
      'D\'ont have any item in your cart',
      name: 'dont_have_any_item_in_your_cart',
      desc: '',
      args: [],
    );
  }

  /// `Start Exploring`
  String get start_exploring {
    return Intl.message(
      'Start Exploring',
      name: 'start_exploring',
      desc: '',
      args: [],
    );
  }

  /// `D'ont have any item in the notification list`
  String get dont_have_any_item_in_the_notification_list {
    return Intl.message(
      'D\'ont have any item in the notification list',
      name: 'dont_have_any_item_in_the_notification_list',
      desc: '',
      args: [],
    );
  }

  /// `Payment Settings`
  String get payment_settings {
    return Intl.message(
      'Payment Settings',
      name: 'payment_settings',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid number`
  String get not_a_valid_number {
    return Intl.message(
      'Not a valid number',
      name: 'not_a_valid_number',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid date`
  String get not_a_valid_date {
    return Intl.message(
      'Not a valid date',
      name: 'not_a_valid_date',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid CVC`
  String get not_a_valid_cvc {
    return Intl.message(
      'Not a valid CVC',
      name: 'not_a_valid_cvc',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Not a valid full name`
  String get not_a_valid_full_name {
    return Intl.message(
      'Not a valid full name',
      name: 'not_a_valid_full_name',
      desc: '',
      args: [],
    );
  }

  /// `Email Address`
  String get email_address {
    return Intl.message(
      'Email Address',
      name: 'email_address',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid email`
  String get not_a_valid_email {
    return Intl.message(
      'Not a valid email',
      name: 'not_a_valid_email',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid phone`
  String get not_a_valid_phone {
    return Intl.message(
      'Not a valid phone',
      name: 'not_a_valid_phone',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid address`
  String get not_a_valid_address {
    return Intl.message(
      'Not a valid address',
      name: 'not_a_valid_address',
      desc: '',
      args: [],
    );
  }

  /// `Not a valid biography`
  String get not_a_valid_biography {
    return Intl.message(
      'Not a valid biography',
      name: 'not_a_valid_biography',
      desc: '',
      args: [],
    );
  }

  /// `Your biography`
  String get your_biography {
    return Intl.message(
      'Your biography',
      name: 'your_biography',
      desc: '',
      args: [],
    );
  }

  /// `Your Address`
  String get your_address {
    return Intl.message(
      'Your Address',
      name: 'your_address',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Recents Search`
  String get recents_search {
    return Intl.message(
      'Recents Search',
      name: 'recents_search',
      desc: '',
      args: [],
    );
  }

  /// `Verify your internet connection`
  String get verify_your_internet_connection {
    return Intl.message(
      'Verify your internet connection',
      name: 'verify_your_internet_connection',
      desc: '',
      args: [],
    );
  }

  /// `Carts refreshed successfully`
  String get carts_refreshed_successfuly {
    return Intl.message(
      'Carts refreshed successfully',
      name: 'carts_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `The $productName was removed from your cart`
  String get the_product_was_removed_from_your_cart {
    return Intl.message(
      'The \$productName was removed from your cart',
      name: 'the_product_was_removed_from_your_cart',
      desc: '',
      args: [],
    );
  }

  /// `Category refreshed successfully`
  String get category_refreshed_successfuly {
    return Intl.message(
      'Category refreshed successfully',
      name: 'category_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Notifications refreshed successfully`
  String get notifications_refreshed_successfuly {
    return Intl.message(
      'Notifications refreshed successfully',
      name: 'notifications_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Order refreshed successfully`
  String get order_refreshed_successfuly {
    return Intl.message(
      'Order refreshed successfully',
      name: 'order_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Orders refreshed successfully`
  String get orders_refreshed_successfuly {
    return Intl.message(
      'Orders refreshed successfully',
      name: 'orders_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Market refreshed successfully`
  String get market_refreshed_successfuly {
    return Intl.message(
      'Market refreshed successfully',
      name: 'market_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Profile settings updated successfully`
  String get profile_settings_updated_successfully {
    return Intl.message(
      'Profile settings updated successfully',
      name: 'profile_settings_updated_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Payment settings updated successfully`
  String get payment_settings_updated_successfully {
    return Intl.message(
      'Payment settings updated successfully',
      name: 'payment_settings_updated_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Tracking refreshed successfully`
  String get tracking_refreshed_successfuly {
    return Intl.message(
      'Tracking refreshed successfully',
      name: 'tracking_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Welcome`
  String get welcome {
    return Intl.message('Welcome', name: 'welcome', desc: '', args: []);
  }

  /// `Wrong email or password`
  String get wrong_email_or_password {
    return Intl.message(
      'Wrong email or password',
      name: 'wrong_email_or_password',
      desc: '',
      args: [],
    );
  }

  /// `Addresses refreshed successfuly`
  String get addresses_refreshed_successfuly {
    return Intl.message(
      'Addresses refreshed successfuly',
      name: 'addresses_refreshed_successfuly',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Addresses`
  String get delivery_addresses {
    return Intl.message(
      'Delivery Addresses',
      name: 'delivery_addresses',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get add {
    return Intl.message('Add', name: 'add', desc: '', args: []);
  }

  /// `New Address added successfully`
  String get new_address_added_successfully {
    return Intl.message(
      'New Address added successfully',
      name: 'new_address_added_successfully',
      desc: '',
      args: [],
    );
  }

  /// `The address updated successfully`
  String get the_address_updated_successfully {
    return Intl.message(
      'The address updated successfully',
      name: 'the_address_updated_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Long press to edit item, swipe item to delete it`
  String get long_press_to_edit_item_swipe_item_to_delete_it {
    return Intl.message(
      'Long press to edit item, swipe item to delete it',
      name: 'long_press_to_edit_item_swipe_item_to_delete_it',
      desc: '',
      args: [],
    );
  }

  /// `Home Address`
  String get home_address {
    return Intl.message(
      'Home Address',
      name: 'home_address',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `12 Street, City 21663, Country`
  String get hint_full_address {
    return Intl.message(
      '12 Street, City 21663, Country',
      name: 'hint_full_address',
      desc: '',
      args: [],
    );
  }

  /// `Full Address`
  String get full_address {
    return Intl.message(
      'Full Address',
      name: 'full_address',
      desc: '',
      args: [],
    );
  }

  /// `Orders`
  String get orders {
    return Intl.message('Orders', name: 'orders', desc: '', args: []);
  }

  /// `History`
  String get history {
    return Intl.message('History', name: 'history', desc: '', args: []);
  }

  /// `Delivered`
  String get delivered {
    return Intl.message('Delivered', name: 'delivered', desc: '', args: []);
  }

  /// `Dismiss`
  String get dismiss {
    return Intl.message('Dismiss', name: 'dismiss', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Confirmed`
  String get confirmed {
    return Intl.message('Confirmed', name: 'confirmed', desc: '', args: []);
  }

  /// `Would you please confirm if you have delivered all meals to client`
  String get would_you_please_confirm_if_you_have_delivered_all_meals {
    return Intl.message(
      'Would you please confirm if you have delivered all meals to client',
      name: 'would_you_please_confirm_if_you_have_delivered_all_meals',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Confirmation`
  String get delivery_confirmation {
    return Intl.message(
      'Delivery Confirmation',
      name: 'delivery_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Products Ordered`
  String get products_ordered {
    return Intl.message(
      'Products Ordered',
      name: 'products_ordered',
      desc: '',
      args: [],
    );
  }

  /// `Order Details`
  String get order_details {
    return Intl.message(
      'Order Details',
      name: 'order_details',
      desc: '',
      args: [],
    );
  }

  /// `Address not provided please call the client`
  String get address_not_provided_please_call_the_client {
    return Intl.message(
      'Address not provided please call the client',
      name: 'address_not_provided_please_call_the_client',
      desc: '',
      args: [],
    );
  }

  /// `Address not provided contact client`
  String get address_not_provided_contact_client {
    return Intl.message(
      'Address not provided contact client',
      name: 'address_not_provided_contact_client',
      desc: '',
      args: [],
    );
  }

  /// `Orders History`
  String get orders_history {
    return Intl.message(
      'Orders History',
      name: 'orders_history',
      desc: '',
      args: [],
    );
  }

  /// `Email to reset password`
  String get email_to_reset_password {
    return Intl.message(
      'Email to reset password',
      name: 'email_to_reset_password',
      desc: '',
      args: [],
    );
  }

  /// `Send link`
  String get send_password_reset_link {
    return Intl.message(
      'Send link',
      name: 'send_password_reset_link',
      desc: '',
      args: [],
    );
  }

  /// `I remember my password return to login`
  String get i_remember_my_password_return_to_login {
    return Intl.message(
      'I remember my password return to login',
      name: 'i_remember_my_password_return_to_login',
      desc: '',
      args: [],
    );
  }

  /// `Your reset link has been sent to your email`
  String get your_reset_link_has_been_sent_to_your_email {
    return Intl.message(
      'Your reset link has been sent to your email',
      name: 'your_reset_link_has_been_sent_to_your_email',
      desc: '',
      args: [],
    );
  }

  /// `Error! Verify email settings`
  String get error_verify_email_settings {
    return Intl.message(
      'Error! Verify email settings',
      name: 'error_verify_email_settings',
      desc: '',
      args: [],
    );
  }

  /// `Order status changed`
  String get order_satatus_changed {
    return Intl.message(
      'Order status changed',
      name: 'order_satatus_changed',
      desc: '',
      args: [],
    );
  }

  /// `New Order from costumer`
  String get new_order_from_costumer {
    return Intl.message(
      'New Order from costumer',
      name: 'new_order_from_costumer',
      desc: '',
      args: [],
    );
  }

  /// `Your have an order assigned to you`
  String get your_have_an_order_assigned_to_you {
    return Intl.message(
      'Your have an order assigned to you',
      name: 'your_have_an_order_assigned_to_you',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get unknown {
    return Intl.message('Unknown', name: 'unknown', desc: '', args: []);
  }

  /// `Ordered Products`
  String get ordered_products {
    return Intl.message(
      'Ordered Products',
      name: 'ordered_products',
      desc: '',
      args: [],
    );
  }

  /// `No Notifications Found`
  String get noNotificationsFound {
    return Intl.message(
      'No Notifications Found',
      name: 'noNotificationsFound',
      desc: '',
      args: [],
    );
  }

  /// `Shipping Cost`
  String get delivery_fee {
    return Intl.message(
      'Shipping Cost',
      name: 'delivery_fee',
      desc: '',
      args: [],
    );
  }

  /// `Items`
  String get items {
    return Intl.message('Items', name: 'items', desc: '', args: []);
  }

  /// `You don't have any order assigned to you!`
  String get you_dont_have_any_order_assigned_to_you {
    return Intl.message(
      'You don\'t have any order assigned to you!',
      name: 'you_dont_have_any_order_assigned_to_you',
      desc: '',
      args: [],
    );
  }

  /// `Swipe left the notification to delete or read / unread it`
  String get swip_left_the_notification_to_delete_or_read__unread {
    return Intl.message(
      'Swipe left the notification to delete or read / unread it',
      name: 'swip_left_the_notification_to_delete_or_read__unread',
      desc: '',
      args: [],
    );
  }

  /// `Customer`
  String get customer {
    return Intl.message('Customer', name: 'customer', desc: '', args: []);
  }

  /// `Quantity`
  String get quantity {
    return Intl.message('Quantity', name: 'quantity', desc: '', args: []);
  }

  /// `This account not exist`
  String get thisAccountNotExist {
    return Intl.message(
      'This account not exist',
      name: 'thisAccountNotExist',
      desc: '',
      args: [],
    );
  }

  /// `Tap back again to leave`
  String get tapBackAgainToLeave {
    return Intl.message(
      'Tap back again to leave',
      name: 'tapBackAgainToLeave',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Address`
  String get deliveryAddress {
    return Intl.message(
      'Delivery Address',
      name: 'deliveryAddress',
      desc: '',
      args: [],
    );
  }

  /// `Full Name`
  String get fullName {
    return Intl.message('Full Name', name: 'fullName', desc: '', args: []);
  }

  /// `View Details`
  String get viewDetails {
    return Intl.message(
      'View Details',
      name: 'viewDetails',
      desc: '',
      args: [],
    );
  }

  /// `View Payment Attachment`
  String get viewAttachment {
    return Intl.message(
      'View Payment Attachment',
      name: 'viewAttachment',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `Total Earnings`
  String get totalEarning {
    return Intl.message(
      'Total Earnings',
      name: 'totalEarning',
      desc: '',
      args: [],
    );
  }

  /// `Total Orders`
  String get totalOrders {
    return Intl.message(
      'Total Orders',
      name: 'totalOrders',
      desc: '',
      args: [],
    );
  }

  /// `Total Markets`
  String get totalMarkets {
    return Intl.message(
      'Total Markets',
      name: 'totalMarkets',
      desc: '',
      args: [],
    );
  }

  /// `Total Products`
  String get totalProducts {
    return Intl.message(
      'Total Products',
      name: 'totalProducts',
      desc: '',
      args: [],
    );
  }

  /// `My Markets`
  String get myMarkets {
    return Intl.message('My Markets', name: 'myMarkets', desc: '', args: []);
  }

  /// `Closed`
  String get closed {
    return Intl.message('Closed', name: 'closed', desc: '', args: []);
  }

  /// `Open`
  String get open {
    return Intl.message('Open', name: 'open', desc: '', args: []);
  }

  /// `Delivering`
  String get delivery {
    return Intl.message('Delivering', name: 'delivery', desc: '', args: []);
  }

  /// `Pickup`
  String get pickup {
    return Intl.message('Pickup', name: 'pickup', desc: '', args: []);
  }

  /// `Information`
  String get information {
    return Intl.message('Information', name: 'information', desc: '', args: []);
  }

  /// `Featured Products`
  String get featuredProducts {
    return Intl.message(
      'Featured Products',
      name: 'featuredProducts',
      desc: '',
      args: [],
    );
  }

  /// `What They Say ?`
  String get whatTheySay {
    return Intl.message(
      'What They Say ?',
      name: 'whatTheySay',
      desc: '',
      args: [],
    );
  }

  /// `View`
  String get view {
    return Intl.message('View', name: 'view', desc: '', args: []);
  }

  /// `Confirmation`
  String get confirmation {
    return Intl.message(
      'Confirmation',
      name: 'confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get yes {
    return Intl.message('Yes', name: 'yes', desc: '', args: []);
  }

  /// `Are you sure you want to cancel this order of customer ?`
  String get areYouSureYouWantToCancelThisOrderOf {
    return Intl.message(
      'Are you sure you want to cancel this order of customer ?',
      name: 'areYouSureYouWantToCancelThisOrderOf',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this size?`
  String get areYouSureYouWantToDeleteThisSize {
    return Intl.message(
      'Are you sure you want to delete this size?',
      name: 'areYouSureYouWantToDeleteThisSize',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete your account ?`
  String get areYouSureYouWantToDeleteYourAccount {
    return Intl.message(
      'Are you sure you want to delete your account ?',
      name: 'areYouSureYouWantToDeleteYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this color?`
  String get areYouSureYouWantToDeleteThisColor {
    return Intl.message(
      'Are you sure you want to delete this color?',
      name: 'areYouSureYouWantToDeleteThisColor',
      desc: '',
      args: [],
    );
  }

  /// `Edit Order`
  String get editOrder {
    return Intl.message('Edit Order', name: 'editOrder', desc: '', args: []);
  }

  /// `This order updated successfully`
  String get thisOrderUpdatedSuccessfully {
    return Intl.message(
      'This order updated successfully',
      name: 'thisOrderUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Assign Delivery Boy`
  String get assignDeliveryBoy {
    return Intl.message(
      'Assign Delivery Boy',
      name: 'assignDeliveryBoy',
      desc: '',
      args: [],
    );
  }

  /// `Hint`
  String get hint {
    return Intl.message('Hint', name: 'hint', desc: '', args: []);
  }

  /// `Insert an additional information for this order`
  String get insertAnAdditionalInformationForThisOrder {
    return Intl.message(
      'Insert an additional information for this order',
      name: 'insertAnAdditionalInformationForThisOrder',
      desc: '',
      args: [],
    );
  }

  /// `Order: #{id} has been canceled`
  String orderIdHasBeenCanceled(Object id) {
    return Intl.message(
      'Order: #$id has been canceled',
      name: 'orderIdHasBeenCanceled',
      desc: '',
      args: [id],
    );
  }

  /// `Canceled`
  String get canceled {
    return Intl.message('Canceled', name: 'canceled', desc: '', args: []);
  }

  /// `Messages`
  String get messages {
    return Intl.message('Messages', name: 'messages', desc: '', args: []);
  }

  /// `You don't have any conversations`
  String get youDontHaveAnyConversations {
    return Intl.message(
      'You don\'t have any conversations',
      name: 'youDontHaveAnyConversations',
      desc: '',
      args: [],
    );
  }

  /// `New message from`
  String get newMessageFrom {
    return Intl.message(
      'New message from',
      name: 'newMessageFrom',
      desc: '',
      args: [],
    );
  }

  /// `Type to start chat`
  String get typeToStartChat {
    return Intl.message(
      'Type to start chat',
      name: 'typeToStartChat',
      desc: '',
      args: [],
    );
  }

  /// `This notification has marked as read`
  String get thisNotificationHasMarkedAsRead {
    return Intl.message(
      'This notification has marked as read',
      name: 'thisNotificationHasMarkedAsRead',
      desc: '',
      args: [],
    );
  }

  /// `This notification has marked as un read`
  String get thisNotificationHasMarkedAsUnRead {
    return Intl.message(
      'This notification has marked as un read',
      name: 'thisNotificationHasMarkedAsUnRead',
      desc: '',
      args: [],
    );
  }

  /// `Notification was removed`
  String get notificationWasRemoved {
    return Intl.message(
      'Notification was removed',
      name: 'notificationWasRemoved',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Address removed successfully`
  String get deliveryAddressRemovedSuccessfully {
    return Intl.message(
      'Delivery Address removed successfully',
      name: 'deliveryAddressRemovedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Faqs refreshed successfully`
  String get faqsRefreshedSuccessfully {
    return Intl.message(
      'Faqs refreshed successfully',
      name: 'faqsRefreshedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Favorites refreshed successfully`
  String get favoritesRefreshedSuccessfully {
    return Intl.message(
      'Favorites refreshed successfully',
      name: 'favoritesRefreshedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `This product was added to favorite`
  String get thisProductWasAddedToFavorite {
    return Intl.message(
      'This product was added to favorite',
      name: 'thisProductWasAddedToFavorite',
      desc: '',
      args: [],
    );
  }

  /// `This product was removed from favorites`
  String get thisProductWasRemovedFromFavorites {
    return Intl.message(
      'This product was removed from favorites',
      name: 'thisProductWasRemovedFromFavorites',
      desc: '',
      args: [],
    );
  }

  /// `Product refreshed successfully`
  String get productRefreshedSuccessfully {
    return Intl.message(
      'Product refreshed successfully',
      name: 'productRefreshedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `You don't have markets, please sign-in using admin panel and open new market`
  String get youDontHaveMarketsPleaseSigninUsingAdminPanelAnd {
    return Intl.message(
      'You don\'t have markets, please sign-in using admin panel and open new market',
      name: 'youDontHaveMarketsPleaseSigninUsingAdminPanelAnd',
      desc: '',
      args: [],
    );
  }

  /// `Go To Home`
  String get goToHome {
    return Intl.message('Go To Home', name: 'goToHome', desc: '', args: []);
  }

  /// `The conversation with #{name} is dismissed`
  String theConversationWithIsDismissed(Object name) {
    return Intl.message(
      'The conversation with #$name is dismissed',
      name: 'theConversationWithIsDismissed',
      desc: '',
      args: [name],
    );
  }

  /// `Add Product`
  String get addProduct {
    return Intl.message('Add Product', name: 'addProduct', desc: '', args: []);
  }

  /// `Edit Product`
  String get editProduct {
    return Intl.message(
      'Edit Product',
      name: 'editProduct',
      desc: '',
      args: [],
    );
  }

  /// `Add category`
  String get addCategory {
    return Intl.message(
      'Add category',
      name: 'addCategory',
      desc: '',
      args: [],
    );
  }

  /// `Edit category`
  String get editCategory {
    return Intl.message(
      'Edit category',
      name: 'editCategory',
      desc: '',
      args: [],
    );
  }

  /// `Click to browse`
  String get clickToBrowse {
    return Intl.message(
      'Click to browse',
      name: 'clickToBrowse',
      desc: '',
      args: [],
    );
  }

  /// `Click to browse Logo`
  String get clickToBrowseLogo {
    return Intl.message(
      'Click to browse Logo',
      name: 'clickToBrowseLogo',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Business Name`
  String get businessName {
    return Intl.message(
      'Business Name',
      name: 'businessName',
      desc: '',
      args: [],
    );
  }

  /// `Sale Price`
  String get salePrice {
    return Intl.message('Sale Price', name: 'salePrice', desc: '', args: []);
  }

  /// `Price`
  String get Price {
    return Intl.message('Price', name: 'Price', desc: '', args: []);
  }

  /// `On Sale`
  String get onSale {
    return Intl.message('On Sale', name: 'onSale', desc: '', args: []);
  }

  /// `Out Of Stock`
  String get outOfStock {
    return Intl.message('Out Of Stock', name: 'outOfStock', desc: '', args: []);
  }

  /// `In Stock`
  String get inStock {
    return Intl.message('In Stock', name: 'inStock', desc: '', args: []);
  }

  /// `Order Details`
  String get orderDetails {
    return Intl.message(
      'Order Details',
      name: 'orderDetails',
      desc: '',
      args: [],
    );
  }

  /// `Order Received`
  String get orderReceived {
    return Intl.message(
      'Order Received',
      name: 'orderReceived',
      desc: '',
      args: [],
    );
  }

  /// `Preparing`
  String get preparing {
    return Intl.message('Preparing', name: 'preparing', desc: '', args: []);
  }

  /// `Ready`
  String get ready {
    return Intl.message('Ready', name: 'ready', desc: '', args: []);
  }

  /// `Added successfully`
  String get addedSuccessfully {
    return Intl.message(
      'Added successfully',
      name: 'addedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Updated successfully`
  String get updatedSuccessfully {
    return Intl.message(
      'Updated successfully',
      name: 'updatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Deleted successfully`
  String get deletedSuccessfully {
    return Intl.message(
      'Deleted successfully',
      name: 'deletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get somethingWentWrong {
    return Intl.message(
      'Something went wrong',
      name: 'somethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `No internet connection!`
  String get noInternet {
    return Intl.message(
      'No internet connection!',
      name: 'noInternet',
      desc: '',
      args: [],
    );
  }

  /// `Cannot connect to server. Please try again later...`
  String get cantConnectToServer {
    return Intl.message(
      'Cannot connect to server. Please try again later...',
      name: 'cantConnectToServer',
      desc: '',
      args: [],
    );
  }

  /// `Please pick an image`
  String get pleasePickImage {
    return Intl.message(
      'Please pick an image',
      name: 'pleasePickImage',
      desc: '',
      args: [],
    );
  }

  /// `Required field`
  String get requiredField {
    return Intl.message(
      'Required field',
      name: 'requiredField',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this category?`
  String get deleteCategoryConfirmationMessage {
    return Intl.message(
      'Are you sure you want to delete this category?',
      name: 'deleteCategoryConfirmationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this category?\nYou will delete main category also because it's last sub category`
  String
  get deleteCategoryConfirmationMessageYouWillDeleteMainCategoryAlsoBecauseItsLastSubCategory {
    return Intl.message(
      'Are you sure you want to delete this category?\nYou will delete main category also because it\'s last sub category',
      name:
          'deleteCategoryConfirmationMessageYouWillDeleteMainCategoryAlsoBecauseItsLastSubCategory',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this product?`
  String get deleteProductConfirmationMessage {
    return Intl.message(
      'Are you sure you want to delete this product?',
      name: 'deleteProductConfirmationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this Banner?`
  String get deleteProductConfirmationBanner {
    return Intl.message(
      'Are you sure you want to delete this Banner?',
      name: 'deleteProductConfirmationBanner',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get price {
    return Intl.message('Price', name: 'price', desc: '', args: []);
  }

  /// `Appearance`
  String get appearance {
    return Intl.message('Appearance', name: 'appearance', desc: '', args: []);
  }

  /// `About us`
  String get aboutUs {
    return Intl.message('About us', name: 'aboutUs', desc: '', args: []);
  }

  /// `Terms & condition`
  String get termsAndCondition {
    return Intl.message(
      'Terms & condition',
      name: 'termsAndCondition',
      desc: '',
      args: [],
    );
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `Completed`
  String get completed {
    return Intl.message('Completed', name: 'completed', desc: '', args: []);
  }

  /// `Pending`
  String get pending {
    return Intl.message('Pending', name: 'pending', desc: '', args: []);
  }

  /// `Refunded`
  String get refunded {
    return Intl.message('Refunded', name: 'refunded', desc: '', args: []);
  }

  /// `Done`
  String get done {
    return Intl.message('Done', name: 'done', desc: '', args: []);
  }

  /// `Done`
  String get taskDone {
    return Intl.message('Done', name: 'taskDone', desc: '', args: []);
  }

  /// `Failed`
  String get failed {
    return Intl.message('Failed', name: 'failed', desc: '', args: []);
  }

  /// `Would you please confirm if you want to save changes`
  String get confirmTheChanges {
    return Intl.message(
      'Would you please confirm if you want to save changes',
      name: 'confirmTheChanges',
      desc: '',
      args: [],
    );
  }

  /// `You don't have any orders 🙂`
  String get doNotHaveAnyOrder {
    return Intl.message(
      'You don\'t have any orders 🙂',
      name: 'doNotHaveAnyOrder',
      desc: '',
      args: [],
    );
  }

  /// `Products`
  String get products {
    return Intl.message('Products', name: 'products', desc: '', args: []);
  }

  /// `Categories`
  String get categories {
    return Intl.message('Categories', name: 'categories', desc: '', args: []);
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Delete Account`
  String get deleteAccount {
    return Intl.message(
      'Delete Account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `Order Status`
  String get orderStatus {
    return Intl.message(
      'Order Status',
      name: 'orderStatus',
      desc: '',
      args: [],
    );
  }

  /// `General Information`
  String get generalInformation {
    return Intl.message(
      'General Information',
      name: 'generalInformation',
      desc: '',
      args: [],
    );
  }

  /// `Save Changes`
  String get saveChanges {
    return Intl.message(
      'Save Changes',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }

  /// `System`
  String get system {
    return Intl.message('System', name: 'system', desc: '', args: []);
  }

  /// `No Users Found`
  String get noUsersFound {
    return Intl.message(
      'No Users Found',
      name: 'noUsersFound',
      desc: '',
      args: [],
    );
  }

  /// `Customers`
  String get customers {
    return Intl.message('Customers', name: 'customers', desc: '', args: []);
  }

  /// `Light`
  String get light {
    return Intl.message('Light', name: 'light', desc: '', args: []);
  }

  /// `state`
  String get state {
    return Intl.message('state', name: 'state', desc: '', args: []);
  }

  /// `City`
  String get city {
    return Intl.message('City', name: 'city', desc: '', args: []);
  }

  /// `Street Name`
  String get streetName {
    return Intl.message('Street Name', name: 'streetName', desc: '', args: []);
  }

  /// `Building`
  String get building {
    return Intl.message('Building', name: 'building', desc: '', args: []);
  }

  /// `Floor`
  String get floor {
    return Intl.message('Floor', name: 'floor', desc: '', args: []);
  }

  /// `Apartment`
  String get apartment {
    return Intl.message('Apartment', name: 'apartment', desc: '', args: []);
  }

  /// `Dark`
  String get dark {
    return Intl.message('Dark', name: 'dark', desc: '', args: []);
  }

  /// `Contact us`
  String get contactUs {
    return Intl.message('Contact us', name: 'contactUs', desc: '', args: []);
  }

  /// `Note`
  String get note {
    return Intl.message('Note', name: 'note', desc: '', args: []);
  }

  /// `Max upload images is only 4`
  String get maxUploadFilesIsOnly4 {
    return Intl.message(
      'Max upload images is only 4',
      name: 'maxUploadFilesIsOnly4',
      desc: '',
      args: [],
    );
  }

  /// `Max upload image size is only 10 MB`
  String get maxUploadFileSizeIsOnly10MB {
    return Intl.message(
      'Max upload image size is only 10 MB',
      name: 'maxUploadFileSizeIsOnly10MB',
      desc: '',
      args: [],
    );
  }

  /// `Facebook`
  String get facebook {
    return Intl.message('Facebook', name: 'facebook', desc: '', args: []);
  }

  /// `Instagram`
  String get instagram {
    return Intl.message('Instagram', name: 'instagram', desc: '', args: []);
  }

  /// `Tiktok`
  String get tiktok {
    return Intl.message('Tiktok', name: 'tiktok', desc: '', args: []);
  }

  /// `Your order status has been changed`
  String get yourOrderStatusHasBeenChanged {
    return Intl.message(
      'Your order status has been changed',
      name: 'yourOrderStatusHasBeenChanged',
      desc: '',
      args: [],
    );
  }

  /// `Your delivery cost has been changed`
  String get yourDeliveryCostHasBeenChanged {
    return Intl.message(
      'Your delivery cost has been changed',
      name: 'yourDeliveryCostHasBeenChanged',
      desc: '',
      args: [],
    );
  }

  /// `Delivery cost changed to {cost}`
  String deliveryCostChangedTo(Object cost) {
    return Intl.message(
      'Delivery cost changed to $cost',
      name: 'deliveryCostChangedTo',
      desc: '',
      args: [cost],
    );
  }

  /// `Order status changed to {status}`
  String orderStatusChangedTo(Object status) {
    return Intl.message(
      'Order status changed to $status',
      name: 'orderStatusChangedTo',
      desc: '',
      args: [status],
    );
  }

  /// `No data`
  String get noData {
    return Intl.message('No data', name: 'noData', desc: '', args: []);
  }

  /// `No products in this category`
  String get noProductsInThisCategory {
    return Intl.message(
      'No products in this category',
      name: 'noProductsInThisCategory',
      desc: '',
      args: [],
    );
  }

  /// `No categories`
  String get noCategories {
    return Intl.message(
      'No categories',
      name: 'noCategories',
      desc: '',
      args: [],
    );
  }

  /// `Colors`
  String get colors {
    return Intl.message('Colors', name: 'colors', desc: '', args: []);
  }

  /// `Color`
  String get color {
    return Intl.message('Color', name: 'color', desc: '', args: []);
  }

  /// `Sizes`
  String get sizes {
    return Intl.message('Sizes', name: 'sizes', desc: '', args: []);
  }

  /// `Size`
  String get size {
    return Intl.message('Size', name: 'size', desc: '', args: []);
  }

  /// `Send Notification`
  String get sendNotification {
    return Intl.message(
      'Send Notification',
      name: 'sendNotification',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get send {
    return Intl.message('Send', name: 'send', desc: '', args: []);
  }

  /// `Settings`
  String get setting {
    return Intl.message('Settings', name: 'setting', desc: '', args: []);
  }

  /// `Add Colors`
  String get addColors {
    return Intl.message('Add Colors', name: 'addColors', desc: '', args: []);
  }

  /// `Add Sizes`
  String get addSizes {
    return Intl.message('Add Sizes', name: 'addSizes', desc: '', args: []);
  }

  /// `Account Settings`
  String get accountSettings {
    return Intl.message(
      'Account Settings',
      name: 'accountSettings',
      desc: '',
      args: [],
    );
  }

  /// `Edit Shipping`
  String get editShipping {
    return Intl.message(
      'Edit Shipping',
      name: 'editShipping',
      desc: '',
      args: [],
    );
  }

  /// `Extra Settings`
  String get extraSettings {
    return Intl.message(
      'Extra Settings',
      name: 'extraSettings',
      desc: '',
      args: [],
    );
  }

  /// `Select`
  String get select {
    return Intl.message('Select', name: 'select', desc: '', args: []);
  }

  /// `Please select`
  String get pleaseSelect {
    return Intl.message(
      'Please select',
      name: 'pleaseSelect',
      desc: '',
      args: [],
    );
  }

  /// `Enter size`
  String get enterSize {
    return Intl.message('Enter size', name: 'enterSize', desc: '', args: []);
  }

  /// `Enter color`
  String get enterColor {
    return Intl.message('Enter color', name: 'enterColor', desc: '', args: []);
  }

  /// `Select categories`
  String get selectCategories {
    return Intl.message(
      'Select categories',
      name: 'selectCategories',
      desc: '',
      args: [],
    );
  }

  /// `Edit size`
  String get editSize {
    return Intl.message('Edit size', name: 'editSize', desc: '', args: []);
  }

  /// `Color already exist`
  String get colorAlreadyExist {
    return Intl.message(
      'Color already exist',
      name: 'colorAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `Pick a color !`
  String get pickAColor {
    return Intl.message(
      'Pick a color !',
      name: 'pickAColor',
      desc: '',
      args: [],
    );
  }

  /// `No Colors Added`
  String get noColorsAdded {
    return Intl.message(
      'No Colors Added',
      name: 'noColorsAdded',
      desc: '',
      args: [],
    );
  }

  /// `No Sizes Added`
  String get noSizesAdded {
    return Intl.message(
      'No Sizes Added',
      name: 'noSizesAdded',
      desc: '',
      args: [],
    );
  }

  /// `Ask me anything\n(Marketing, Sales, Tech, etc...)`
  String get askMeAnything {
    return Intl.message(
      'Ask me anything\n(Marketing, Sales, Tech, etc...)',
      name: 'askMeAnything',
      desc: '',
      args: [],
    );
  }

  /// `Ask me anything...`
  String get aiHintMessage {
    return Intl.message(
      'Ask me anything...',
      name: 'aiHintMessage',
      desc: '',
      args: [],
    );
  }

  /// `Enter your message !`
  String get enterYourMessage {
    return Intl.message(
      'Enter your message !',
      name: 'enterYourMessage',
      desc: '',
      args: [],
    );
  }

  /// `Limit reached`
  String get categoryLimitReached {
    return Intl.message(
      'Limit reached',
      name: 'categoryLimitReached',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade your plan now and unlock unlimited addition, Enjoy full access without restrictions and grow your store seamlessly`
  String get categoryLimitReachedDesc {
    return Intl.message(
      'Upgrade your plan now and unlock unlimited addition, Enjoy full access without restrictions and grow your store seamlessly',
      name: 'categoryLimitReachedDesc',
      desc: '',
      args: [],
    );
  }

  /// `Banner limit reached !`
  String get bannerLimitReached {
    return Intl.message(
      'Banner limit reached !',
      name: 'bannerLimitReached',
      desc: '',
      args: [],
    );
  }

  /// `Products limit reached !\nPlease upgrade your plan...`
  String get productLimitReached {
    return Intl.message(
      'Products limit reached !\nPlease upgrade your plan...',
      name: 'productLimitReached',
      desc: '',
      args: [],
    );
  }

  /// `Your subscription has expired`
  String get yourSubscriptionHasExpired {
    return Intl.message(
      'Your subscription has expired',
      name: 'yourSubscriptionHasExpired',
      desc: '',
      args: [],
    );
  }

  /// `Please contact support`
  String get pleaseContactSupport {
    return Intl.message(
      'Please contact support',
      name: 'pleaseContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account?`
  String get dontHaveAccount {
    return Intl.message(
      'Don\'t have an account?',
      name: 'dontHaveAccount',
      desc: '',
      args: [],
    );
  }

  /// `Phone is invalid`
  String get phoneIsInvalid {
    return Intl.message(
      'Phone is invalid',
      name: 'phoneIsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Please select Sub Categories`
  String get pleaseSelectSubCategories {
    return Intl.message(
      'Please select Sub Categories',
      name: 'pleaseSelectSubCategories',
      desc: '',
      args: [],
    );
  }

  /// `Email is invalid`
  String get emailIsInvalid {
    return Intl.message(
      'Email is invalid',
      name: 'emailIsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get date {
    return Intl.message('Date', name: 'date', desc: '', args: []);
  }

  /// `Order Total`
  String get orderTotal {
    return Intl.message('Order Total', name: 'orderTotal', desc: '', args: []);
  }

  /// `Email already exist`
  String get emailAlreadyExist {
    return Intl.message(
      'Email already exist',
      name: 'emailAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `No products found`
  String get noProductsFound {
    return Intl.message(
      'No products found',
      name: 'noProductsFound',
      desc: '',
      args: [],
    );
  }

  /// `Store`
  String get store {
    return Intl.message('Store', name: 'store', desc: '', args: []);
  }

  /// `Invoices`
  String get invoices {
    return Intl.message('Invoices', name: 'invoices', desc: '', args: []);
  }

  /// `Checked Out Successfully`
  String get checkedOutSuccessfully {
    return Intl.message(
      'Checked Out Successfully',
      name: 'checkedOutSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Sent Successfully`
  String get sendSuccessfully {
    return Intl.message(
      'Sent Successfully',
      name: 'sendSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Add To Cart`
  String get addToCart {
    return Intl.message('Add To Cart', name: 'addToCart', desc: '', args: []);
  }

  /// `Product`
  String get product {
    return Intl.message('Product', name: 'product', desc: '', args: []);
  }

  /// `Customer Information`
  String get customerInfo {
    return Intl.message(
      'Customer Information',
      name: 'customerInfo',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe Now`
  String get subscribeNow {
    return Intl.message(
      'Subscribe Now',
      name: 'subscribeNow',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe`
  String get subscribe {
    return Intl.message('Subscribe', name: 'subscribe', desc: '', args: []);
  }

  /// `Users`
  String get users {
    return Intl.message('Users', name: 'users', desc: '', args: []);
  }

  /// `Date & Time`
  String get dateAndTime {
    return Intl.message('Date & Time', name: 'dateAndTime', desc: '', args: []);
  }

  /// `Search Product`
  String get searchProduct {
    return Intl.message(
      'Search Product',
      name: 'searchProduct',
      desc: '',
      args: [],
    );
  }

  /// `Tasks`
  String get tasks {
    return Intl.message('Tasks', name: 'tasks', desc: '', args: []);
  }

  /// `Expenses`
  String get expenses {
    return Intl.message('Expenses', name: 'expenses', desc: '', args: []);
  }

  /// `Add Expenses`
  String get addExpenses {
    return Intl.message(
      'Add Expenses',
      name: 'addExpenses',
      desc: '',
      args: [],
    );
  }

  /// `Add Task`
  String get addTask {
    return Intl.message('Add Task', name: 'addTask', desc: '', args: []);
  }

  /// `Edit Task`
  String get editTask {
    return Intl.message('Edit Task', name: 'editTask', desc: '', args: []);
  }

  /// `Is Paid`
  String get isPaid {
    return Intl.message('Is Paid', name: 'isPaid', desc: '', args: []);
  }

  /// `Is Done`
  String get isDone {
    return Intl.message('Is Done', name: 'isDone', desc: '', args: []);
  }

  /// `Paid`
  String get paid {
    return Intl.message('Paid', name: 'paid', desc: '', args: []);
  }

  /// `UnPaid`
  String get unpaid {
    return Intl.message('UnPaid', name: 'unpaid', desc: '', args: []);
  }

  /// `Delete Task`
  String get deleteTask {
    return Intl.message('Delete Task', name: 'deleteTask', desc: '', args: []);
  }

  /// `Delete Expense`
  String get deleteExpense {
    return Intl.message(
      'Delete Expense',
      name: 'deleteExpense',
      desc: '',
      args: [],
    );
  }

  /// `Total Tasks`
  String get totalTasks {
    return Intl.message('Total Tasks', name: 'totalTasks', desc: '', args: []);
  }

  /// `Uncompleted`
  String get uncompleted {
    return Intl.message('Uncompleted', name: 'uncompleted', desc: '', args: []);
  }

  /// `Sub Categories`
  String get subCategories {
    return Intl.message(
      'Sub Categories',
      name: 'subCategories',
      desc: '',
      args: [],
    );
  }

  /// `Show Pricing`
  String get showPricing {
    return Intl.message(
      'Show Pricing',
      name: 'showPricing',
      desc: '',
      args: [],
    );
  }

  /// `Are You Sure You Want To Delete This Task ?`
  String get areYouSureYouWantToDeleteThisTask {
    return Intl.message(
      'Are You Sure You Want To Delete This Task ?',
      name: 'areYouSureYouWantToDeleteThisTask',
      desc: '',
      args: [],
    );
  }

  /// `Are You Sure You Want To Delete This Expense ?`
  String get areYouSureYouWantToDeleteThisExpense {
    return Intl.message(
      'Are You Sure You Want To Delete This Expense ?',
      name: 'areYouSureYouWantToDeleteThisExpense',
      desc: '',
      args: [],
    );
  }

  /// `Are You Sure You Want To Delete This Payment Method ?`
  String get areYouSureYouWantToDeleteThisPayment {
    return Intl.message(
      'Are You Sure You Want To Delete This Payment Method ?',
      name: 'areYouSureYouWantToDeleteThisPayment',
      desc: '',
      args: [],
    );
  }

  /// `No Data Found`
  String get noDataFound {
    return Intl.message(
      'No Data Found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Edited Successfully`
  String get editedSuccessfully {
    return Intl.message(
      'Edited Successfully',
      name: 'editedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Your Settings`
  String get yourSettings {
    return Intl.message(
      'Your Settings',
      name: 'yourSettings',
      desc: '',
      args: [],
    );
  }

  /// `Sale Price must be less than Price`
  String get salePriceMustBeLessThanPrice {
    return Intl.message(
      'Sale Price must be less than Price',
      name: 'salePriceMustBeLessThanPrice',
      desc: '',
      args: [],
    );
  }

  /// `Sizes & Colors`
  String get sizesAndColors {
    return Intl.message(
      'Sizes & Colors',
      name: 'sizesAndColors',
      desc: '',
      args: [],
    );
  }

  /// `Banners`
  String get banners {
    return Intl.message('Banners', name: 'banners', desc: '', args: []);
  }

  /// `No Banners`
  String get noBanners {
    return Intl.message('No Banners', name: 'noBanners', desc: '', args: []);
  }

  /// `No Expenses`
  String get noExpenses {
    return Intl.message('No Expenses', name: 'noExpenses', desc: '', args: []);
  }

  /// `No Tasks`
  String get noTasks {
    return Intl.message('No Tasks', name: 'noTasks', desc: '', args: []);
  }

  /// `Add Banner`
  String get addBanner {
    return Intl.message('Add Banner', name: 'addBanner', desc: '', args: []);
  }

  /// `Edit Banner`
  String get editBanner {
    return Intl.message('Edit Banner', name: 'editBanner', desc: '', args: []);
  }

  /// `3000W X 1200H is a great size for Banner`
  String get bestImageSize {
    return Intl.message(
      '3000W X 1200H is a great size for Banner',
      name: 'bestImageSize',
      desc: '',
      args: [],
    );
  }

  /// `App Settings`
  String get appSettings {
    return Intl.message(
      'App Settings',
      name: 'appSettings',
      desc: '',
      args: [],
    );
  }

  /// `Optional`
  String get optional {
    return Intl.message('Optional', name: 'optional', desc: '', args: []);
  }

  /// `Title`
  String get title {
    return Intl.message('Title', name: 'title', desc: '', args: []);
  }

  /// `Inventory`
  String get inventory {
    return Intl.message('Inventory', name: 'inventory', desc: '', args: []);
  }

  /// `Website`
  String get website {
    return Intl.message('Website', name: 'website', desc: '', args: []);
  }

  /// `Payment Methods`
  String get paymentMethods {
    return Intl.message(
      'Payment Methods',
      name: 'paymentMethods',
      desc: '',
      args: [],
    );
  }

  /// `My Website`
  String get myWebsite {
    return Intl.message('My Website', name: 'myWebsite', desc: '', args: []);
  }

  /// `Choose Payment Methods`
  String get choosePaymentMethods {
    return Intl.message(
      'Choose Payment Methods',
      name: 'choosePaymentMethods',
      desc: '',
      args: [],
    );
  }

  /// `Add Payment Method`
  String get addPaymentMethod {
    return Intl.message(
      'Add Payment Method',
      name: 'addPaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Edit Payment Method`
  String get editPaymentMethod {
    return Intl.message(
      'Edit Payment Method',
      name: 'editPaymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Bank Account`
  String get bankAccount {
    return Intl.message(
      'Bank Account',
      name: 'bankAccount',
      desc: '',
      args: [],
    );
  }

  /// `Or`
  String get Or {
    return Intl.message('Or', name: 'Or', desc: '', args: []);
  }

  /// `Welcome To IDEA2APP - You can see your website from this link below.\nWe refer to adding your products first to see your website in the best way.`
  String get webLinkMessage {
    return Intl.message(
      'Welcome To IDEA2APP - You can see your website from this link below.\nWe refer to adding your products first to see your website in the best way.',
      name: 'webLinkMessage',
      desc: '',
      args: [],
    );
  }

  /// `Shipping Cost`
  String get shippingCost {
    return Intl.message(
      'Shipping Cost',
      name: 'shippingCost',
      desc: '',
      args: [],
    );
  }

  /// `Copied to clipboard`
  String get copiedToClipboard {
    return Intl.message(
      'Copied to clipboard',
      name: 'copiedToClipboard',
      desc: '',
      args: [],
    );
  }

  /// `Featured`
  String get featured {
    return Intl.message('Featured', name: 'featured', desc: '', args: []);
  }

  /// `Add Stock`
  String get addStocks {
    return Intl.message('Add Stock', name: 'addStocks', desc: '', args: []);
  }

  /// `Area`
  String get area {
    return Intl.message('Area', name: 'area', desc: '', args: []);
  }

  /// `You must turn off inventory first`
  String get youMustTurnOffInventoryFirst {
    return Intl.message(
      'You must turn off inventory first',
      name: 'youMustTurnOffInventoryFirst',
      desc: '',
      args: [],
    );
  }

  /// `You must turn on inventory first`
  String get youMustTurnOnInventoryFirst {
    return Intl.message(
      'You must turn on inventory first',
      name: 'youMustTurnOnInventoryFirst',
      desc: '',
      args: [],
    );
  }

  /// `On`
  String get on {
    return Intl.message('On', name: 'on', desc: '', args: []);
  }

  /// `Off`
  String get off {
    return Intl.message('Off', name: 'off', desc: '', args: []);
  }

  /// `Phone 2`
  String get phone2 {
    return Intl.message('Phone 2', name: 'phone2', desc: '', args: []);
  }

  /// `Colors & Sizes Stock`
  String get colorsAndSizesStock {
    return Intl.message(
      'Colors & Sizes Stock',
      name: 'colorsAndSizesStock',
      desc: '',
      args: [],
    );
  }

  /// `If you turn on the inventory you will remove size & colors stock`
  String get ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock {
    return Intl.message(
      'If you turn on the inventory you will remove size & colors stock',
      name: 'ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock',
      desc: '',
      args: [],
    );
  }

  /// `If you turn off the inventory you will remove the product stock`
  String get ifYouTurnOofTheInventoryYouWillRemoveTheProductStock {
    return Intl.message(
      'If you turn off the inventory you will remove the product stock',
      name: 'ifYouTurnOofTheInventoryYouWillRemoveTheProductStock',
      desc: '',
      args: [],
    );
  }

  /// `You can't change the status`
  String get cantChangeStatus {
    return Intl.message(
      'You can\'t change the status',
      name: 'cantChangeStatus',
      desc: '',
      args: [],
    );
  }

  /// `If you don't need to manage product stock make sure to turn on the inventory and leave it empty`
  String
  get ifYouDontNeedToManageProductStockMakeSureToTurnOnTheInventoryAndLeaveItEmpty {
    return Intl.message(
      'If you don\'t need to manage product stock make sure to turn on the inventory and leave it empty',
      name:
          'ifYouDontNeedToManageProductStockMakeSureToTurnOnTheInventoryAndLeaveItEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Add Colors Stock`
  String get addColorsStock {
    return Intl.message(
      'Add Colors Stock',
      name: 'addColorsStock',
      desc: '',
      args: [],
    );
  }

  /// `Add Sizes Stock`
  String get addSizesStock {
    return Intl.message(
      'Add Sizes Stock',
      name: 'addSizesStock',
      desc: '',
      args: [],
    );
  }

  /// `You must `
  String get youMust {
    return Intl.message('You must ', name: 'youMust', desc: '', args: []);
  }

  /// `Contact Support `
  String get ContactSupport {
    return Intl.message(
      'Contact Support ',
      name: 'ContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `To activate your online payment`
  String get ToActivateYourOnlinePayment {
    return Intl.message(
      'To activate your online payment',
      name: 'ToActivateYourOnlinePayment',
      desc: '',
      args: [],
    );
  }

  /// `Sender Information`
  String get senderInfo {
    return Intl.message(
      'Sender Information',
      name: 'senderInfo',
      desc: '',
      args: [],
    );
  }

  /// `Waiting Payment...`
  String get waitingPayment {
    return Intl.message(
      'Waiting Payment...',
      name: 'waitingPayment',
      desc: '',
      args: [],
    );
  }

  /// `No Payment Methods`
  String get noPaymentMethods {
    return Intl.message(
      'No Payment Methods',
      name: 'noPaymentMethods',
      desc: '',
      args: [],
    );
  }

  /// `Business Type`
  String get businessType {
    return Intl.message(
      'Business Type',
      name: 'businessType',
      desc: '',
      args: [],
    );
  }

  /// `Template`
  String get template {
    return Intl.message('Template', name: 'template', desc: '', args: []);
  }

  /// `Template`
  String get theTemplate {
    return Intl.message('Template', name: 'theTemplate', desc: '', args: []);
  }

  /// `Select your business type`
  String get selectYourBusinessType {
    return Intl.message(
      'Select your business type',
      name: 'selectYourBusinessType',
      desc: '',
      args: [],
    );
  }

  /// `Order Id`
  String get orderId {
    return Intl.message('Order Id', name: 'orderId', desc: '', args: []);
  }

  /// `User Name`
  String get userName {
    return Intl.message('User Name', name: 'userName', desc: '', args: []);
  }

  /// `User Phone`
  String get userPhone {
    return Intl.message('User Phone', name: 'userPhone', desc: '', args: []);
  }

  /// `Invoice Id`
  String get invoiceId {
    return Intl.message('Invoice Id', name: 'invoiceId', desc: '', args: []);
  }

  /// `Search By`
  String get searchBy {
    return Intl.message('Search By', name: 'searchBy', desc: '', args: []);
  }

  /// `Filter By Date`
  String get filterByDate {
    return Intl.message(
      'Filter By Date',
      name: 'filterByDate',
      desc: '',
      args: [],
    );
  }

  /// `Clear Filter`
  String get clearFilter {
    return Intl.message(
      'Clear Filter',
      name: 'clearFilter',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message('Reports', name: 'reports', desc: '', args: []);
  }

  /// `Today`
  String get today {
    return Intl.message('Today', name: 'today', desc: '', args: []);
  }

  /// `Last Week`
  String get lastWeek {
    return Intl.message('Last Week', name: 'lastWeek', desc: '', args: []);
  }

  /// `Last Month`
  String get lastMonth {
    return Intl.message('Last Month', name: 'lastMonth', desc: '', args: []);
  }

  /// `Last Year`
  String get lastYear {
    return Intl.message('Last Year', name: 'lastYear', desc: '', args: []);
  }

  /// `From`
  String get from {
    return Intl.message('From', name: 'from', desc: '', args: []);
  }

  /// `To`
  String get to {
    return Intl.message('To', name: 'to', desc: '', args: []);
  }

  /// `No Orders Found`
  String get noOrdersFound {
    return Intl.message(
      'No Orders Found',
      name: 'noOrdersFound',
      desc: '',
      args: [],
    );
  }

  /// `Last Quarter`
  String get lastQuarter {
    return Intl.message(
      'Last Quarter',
      name: 'lastQuarter',
      desc: '',
      args: [],
    );
  }

  /// `Last Semi Annual`
  String get lastSemiAnnual {
    return Intl.message(
      'Last Semi Annual',
      name: 'lastSemiAnnual',
      desc: '',
      args: [],
    );
  }

  /// `No Data Available`
  String get noDataAvailable {
    return Intl.message(
      'No Data Available',
      name: 'noDataAvailable',
      desc: '',
      args: [],
    );
  }

  /// `No Store Invoices`
  String get noStoreInvoices {
    return Intl.message(
      'No Store Invoices',
      name: 'noStoreInvoices',
      desc: '',
      args: [],
    );
  }

  /// `Store Invoices`
  String get storeInvoices {
    return Intl.message(
      'Store Invoices',
      name: 'storeInvoices',
      desc: '',
      args: [],
    );
  }

  /// `Plan`
  String get plan {
    return Intl.message('Plan', name: 'plan', desc: '', args: []);
  }

  /// `Free`
  String get free {
    return Intl.message('Free', name: 'free', desc: '', args: []);
  }

  /// `Monthly`
  String get monthly {
    return Intl.message('Monthly', name: 'monthly', desc: '', args: []);
  }

  /// `3 Months`
  String get threeMonths {
    return Intl.message('3 Months', name: 'threeMonths', desc: '', args: []);
  }

  /// `6 Months`
  String get sixMonths {
    return Intl.message('6 Months', name: 'sixMonths', desc: '', args: []);
  }

  /// `Annually`
  String get annually {
    return Intl.message('Annually', name: 'annually', desc: '', args: []);
  }

  /// `Deleted`
  String get deleted {
    return Intl.message('Deleted', name: 'deleted', desc: '', args: []);
  }

  /// `My Website Link`
  String get myWebsiteLink {
    return Intl.message(
      'My Website Link',
      name: 'myWebsiteLink',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get expired {
    return Intl.message('Expired', name: 'expired', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Days`
  String get days {
    return Intl.message('Days', name: 'days', desc: '', args: []);
  }

  /// `ago`
  String get ago {
    return Intl.message('ago', name: 'ago', desc: '', args: []);
  }

  /// `Renew Now`
  String get renewNow {
    return Intl.message('Renew Now', name: 'renewNow', desc: '', args: []);
  }

  /// `left`
  String get left {
    return Intl.message('left', name: 'left', desc: '', args: []);
  }

  /// `Earnings`
  String get earnings {
    return Intl.message('Earnings', name: 'earnings', desc: '', args: []);
  }

  /// `Steps to create your free website`
  String get stepsToCreateYourFreeWebSite {
    return Intl.message(
      'Steps to create your free website',
      name: 'stepsToCreateYourFreeWebSite',
      desc: '',
      args: [],
    );
  }

  /// `Add your first category`
  String get addYourFirstCategory {
    return Intl.message(
      'Add your first category',
      name: 'addYourFirstCategory',
      desc: '',
      args: [],
    );
  }

  /// `Add your first product`
  String get addYourFirstProduct {
    return Intl.message(
      'Add your first product',
      name: 'addYourFirstProduct',
      desc: '',
      args: [],
    );
  }

  /// `Add your first banner`
  String get addYourFirstBanner {
    return Intl.message(
      'Add your first banner',
      name: 'addYourFirstBanner',
      desc: '',
      args: [],
    );
  }

  /// `Please add category first`
  String get pleaseAddCategoryFirst {
    return Intl.message(
      'Please add category first',
      name: 'pleaseAddCategoryFirst',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get apply {
    return Intl.message('Apply', name: 'apply', desc: '', args: []);
  }

  /// `Filter With Date Range`
  String get filterWithDateRange {
    return Intl.message(
      'Filter With Date Range',
      name: 'filterWithDateRange',
      desc: '',
      args: [],
    );
  }

  /// `It's not active and will not be displayed to your customers`
  String get itsNotActiveAndWillNotBeDisplayedToYourCustomers {
    return Intl.message(
      'It\'s not active and will not be displayed to your customers',
      name: 'itsNotActiveAndWillNotBeDisplayedToYourCustomers',
      desc: '',
      args: [],
    );
  }

  /// `Website Name`
  String get websiteName {
    return Intl.message(
      'Website Name',
      name: 'websiteName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Website Name`
  String get enterWebsiteName {
    return Intl.message(
      'Enter Website Name',
      name: 'enterWebsiteName',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Website Name`
  String get invalidWebsiteName {
    return Intl.message(
      'Invalid Website Name',
      name: 'invalidWebsiteName',
      desc: '',
      args: [],
    );
  }

  /// `Free Website`
  String get freeWebsite {
    return Intl.message(
      'Free Website',
      name: 'freeWebsite',
      desc: '',
      args: [],
    );
  }

  /// `Premium Website`
  String get premiumWebsite {
    return Intl.message(
      'Premium Website',
      name: 'premiumWebsite',
      desc: '',
      args: [],
    );
  }

  /// `(No domain fees)`
  String get noDomainFees {
    return Intl.message(
      '(No domain fees)',
      name: 'noDomainFees',
      desc: '',
      args: [],
    );
  }

  /// `If you need to change your website name please`
  String get ifYouNeedToChangeYourWebsiteNamePlease {
    return Intl.message(
      'If you need to change your website name please',
      name: 'ifYouNeedToChangeYourWebsiteNamePlease',
      desc: '',
      args: [],
    );
  }

  /// `Website Name already exist`
  String get websiteNameAlreadyExist {
    return Intl.message(
      'Website Name already exist',
      name: 'websiteNameAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `Email & Website Name already exist`
  String get emailAndWebsiteNameAlreadyExist {
    return Intl.message(
      'Email & Website Name already exist',
      name: 'emailAndWebsiteNameAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `Vendors`
  String get vendors {
    return Intl.message('Vendors', name: 'vendors', desc: '', args: []);
  }

  /// `Add New Vendor`
  String get addNewVendor {
    return Intl.message(
      'Add New Vendor',
      name: 'addNewVendor',
      desc: '',
      args: [],
    );
  }

  /// `Edit Vendor`
  String get editVendor {
    return Intl.message('Edit Vendor', name: 'editVendor', desc: '', args: []);
  }

  /// `Is Active`
  String get isActive {
    return Intl.message('Is Active', name: 'isActive', desc: '', args: []);
  }

  /// `Expire Date`
  String get expireDate {
    return Intl.message('Expire Date', name: 'expireDate', desc: '', args: []);
  }

  /// `Vendor deleted successfully`
  String get vendorDeletedSuccessfully {
    return Intl.message(
      'Vendor deleted successfully',
      name: 'vendorDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `No Vendors Found`
  String get noVendorsFound {
    return Intl.message(
      'No Vendors Found',
      name: 'noVendorsFound',
      desc: '',
      args: [],
    );
  }

  /// `Vendor Status`
  String get vendorStatus {
    return Intl.message(
      'Vendor Status',
      name: 'vendorStatus',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message('Start Date', name: 'startDate', desc: '', args: []);
  }

  /// `Paid Amount`
  String get paidAmount {
    return Intl.message('Paid Amount', name: 'paidAmount', desc: '', args: []);
  }

  /// `Subscription Type`
  String get subscriptionType {
    return Intl.message(
      'Subscription Type',
      name: 'subscriptionType',
      desc: '',
      args: [],
    );
  }

  /// `Pricing Plan`
  String get pricingPlan {
    return Intl.message(
      'Pricing Plan',
      name: 'pricingPlan',
      desc: '',
      args: [],
    );
  }

  /// `Remaining Amount`
  String get remainingAmount {
    return Intl.message(
      'Remaining Amount',
      name: 'remainingAmount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this vendor?`
  String get areYouSureYouWantToDeleteThisVendor {
    return Intl.message(
      'Are you sure you want to delete this vendor?',
      name: 'areYouSureYouWantToDeleteThisVendor',
      desc: '',
      args: [],
    );
  }

  /// `Steps to create your website`
  String get stepsToCreateYourWebSite {
    return Intl.message(
      'Steps to create your website',
      name: 'stepsToCreateYourWebSite',
      desc: '',
      args: [],
    );
  }

  /// `One Month`
  String get oneMonth {
    return Intl.message('One Month', name: 'oneMonth', desc: '', args: []);
  }

  /// `One Time Price`
  String get oneTimePrice {
    return Intl.message(
      'One Time Price',
      name: 'oneTimePrice',
      desc: '',
      args: [],
    );
  }

  /// `Choose Your Plan`
  String get chooseYourPlan {
    return Intl.message(
      'Choose Your Plan',
      name: 'chooseYourPlan',
      desc: '',
      args: [],
    );
  }

  /// `Pay Now`
  String get payNow {
    return Intl.message('Pay Now', name: 'payNow', desc: '', args: []);
  }

  /// `Payment Attachment`
  String get paymentAttachment {
    return Intl.message(
      'Payment Attachment',
      name: 'paymentAttachment',
      desc: '',
      args: [],
    );
  }

  /// `Pay with Instapay`
  String get payWithInstapay {
    return Intl.message(
      'Pay with Instapay',
      name: 'payWithInstapay',
      desc: '',
      args: [],
    );
  }

  /// `Pay with Vodafone Cash`
  String get payWithVodafoneCash {
    return Intl.message(
      'Pay with Vodafone Cash',
      name: 'payWithVodafoneCash',
      desc: '',
      args: [],
    );
  }

  /// `Subscription Plan`
  String get subscriptionPlan {
    return Intl.message(
      'Subscription Plan',
      name: 'subscriptionPlan',
      desc: '',
      args: [],
    );
  }

  /// `Payment sent successfully`
  String get paymentSentSuccessfully {
    return Intl.message(
      'Payment sent successfully',
      name: 'paymentSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Your request has been successfully sent and is currently under review. We will provide a response as soon as possible.`
  String get yourRequestSentSuccessfully {
    return Intl.message(
      'Your request has been successfully sent and is currently under review. We will provide a response as soon as possible.',
      name: 'yourRequestSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Send Request`
  String get sendRequest {
    return Intl.message(
      'Send Request',
      name: 'sendRequest',
      desc: '',
      args: [],
    );
  }

  /// `Please attach payment attachment`
  String get pleaseAttachPaymentAttachment {
    return Intl.message(
      'Please attach payment attachment',
      name: 'pleaseAttachPaymentAttachment',
      desc: '',
      args: [],
    );
  }

  /// `Social Links & About`
  String get socialLinks {
    return Intl.message(
      'Social Links & About',
      name: 'socialLinks',
      desc: '',
      args: [],
    );
  }

  /// `About & Privacy`
  String get aboutAndPrivacy {
    return Intl.message(
      'About & Privacy',
      name: 'aboutAndPrivacy',
      desc: '',
      args: [],
    );
  }

  /// `Your Facebook Link`
  String get YourFacebookLink {
    return Intl.message(
      'Your Facebook Link',
      name: 'YourFacebookLink',
      desc: '',
      args: [],
    );
  }

  /// `Your Instagram Link`
  String get YourInstagramLink {
    return Intl.message(
      'Your Instagram Link',
      name: 'YourInstagramLink',
      desc: '',
      args: [],
    );
  }

  /// `Your Tiktok Link`
  String get YourTiktokLink {
    return Intl.message(
      'Your Tiktok Link',
      name: 'YourTiktokLink',
      desc: '',
      args: [],
    );
  }

  /// `Your WhatsApp Number`
  String get YourWhatsApp {
    return Intl.message(
      'Your WhatsApp Number',
      name: 'YourWhatsApp',
      desc: '',
      args: [],
    );
  }

  /// `If you fill in any of these fields, they will be visible to your clients. If you leave any of them empty, those specific links will not be shown.`
  String get socialLinksHint {
    return Intl.message(
      'If you fill in any of these fields, they will be visible to your clients. If you leave any of them empty, those specific links will not be shown.',
      name: 'socialLinksHint',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid link`
  String get enterValidLink {
    return Intl.message(
      'Enter a valid link',
      name: 'enterValidLink',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Password length should be more than 8 characters`
  String get passwordLength {
    return Intl.message(
      'Password length should be more than 8 characters',
      name: 'passwordLength',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid number`
  String get enterValidNumber {
    return Intl.message(
      'Enter a valid number',
      name: 'enterValidNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid email`
  String get enterValidEmail {
    return Intl.message(
      'Enter a valid email',
      name: 'enterValidEmail',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid phone number`
  String get enterValidPhoneNumber {
    return Intl.message(
      'Enter a valid phone number',
      name: 'enterValidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Subscription Requests`
  String get subscriptionRequests {
    return Intl.message(
      'Subscription Requests',
      name: 'subscriptionRequests',
      desc: '',
      args: [],
    );
  }

  /// `Payment Method`
  String get paymentMethod {
    return Intl.message(
      'Payment Method',
      name: 'paymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Request Date`
  String get requestDate {
    return Intl.message(
      'Request Date',
      name: 'requestDate',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this subscription request?`
  String get areYouSureYouWantToDeleteThisSubscriptionRequest {
    return Intl.message(
      'Are you sure you want to delete this subscription request?',
      name: 'areYouSureYouWantToDeleteThisSubscriptionRequest',
      desc: '',
      args: [],
    );
  }

  /// `Approved`
  String get approved {
    return Intl.message('Approved', name: 'approved', desc: '', args: []);
  }

  /// `Approve Subscription Request`
  String get approveSubscriptionRequest {
    return Intl.message(
      'Approve Subscription Request',
      name: 'approveSubscriptionRequest',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to approve this subscription request?`
  String get areYouSureYouWantToApproveThisSubscriptionRequest {
    return Intl.message(
      'Are you sure you want to approve this subscription request?',
      name: 'areYouSureYouWantToApproveThisSubscriptionRequest',
      desc: '',
      args: [],
    );
  }

  /// `Your account is not active`
  String get yourAccountIsNotActive {
    return Intl.message(
      'Your account is not active',
      name: 'yourAccountIsNotActive',
      desc: '',
      args: [],
    );
  }

  /// `Your account is not active, please renew your subscription !`
  String get yourAccountIsNotActivePleaseRenewYourSubscription {
    return Intl.message(
      'Your account is not active, please renew your subscription !',
      name: 'yourAccountIsNotActivePleaseRenewYourSubscription',
      desc: '',
      args: [],
    );
  }

  /// `Approve Request`
  String get approveRequest {
    return Intl.message(
      'Approve Request',
      name: 'approveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Approve`
  String get approve {
    return Intl.message('Approve', name: 'approve', desc: '', args: []);
  }

  /// `Request Approved`
  String get requestApproved {
    return Intl.message(
      'Request Approved',
      name: 'requestApproved',
      desc: '',
      args: [],
    );
  }

  /// `Subscription request approved successfully`
  String get subscriptionRequestApprovedSuccessfully {
    return Intl.message(
      'Subscription request approved successfully',
      name: 'subscriptionRequestApprovedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Change Plan`
  String get changePlan {
    return Intl.message('Change Plan', name: 'changePlan', desc: '', args: []);
  }

  /// `phone number or pay link`
  String get phoneOrPayLink {
    return Intl.message(
      'phone number or pay link',
      name: 'phoneOrPayLink',
      desc: '',
      args: [],
    );
  }

  /// `No result found`
  String get noResultFound {
    return Intl.message(
      'No result found',
      name: 'noResultFound',
      desc: '',
      args: [],
    );
  }

  /// `Select Vendor`
  String get selectVendor {
    return Intl.message(
      'Select Vendor',
      name: 'selectVendor',
      desc: '',
      args: [],
    );
  }

  /// `No Subscription Requests`
  String get noSubscriptionRequests {
    return Intl.message(
      'No Subscription Requests',
      name: 'noSubscriptionRequests',
      desc: '',
      args: [],
    );
  }

  /// `Discount`
  String get discount {
    return Intl.message('Discount', name: 'discount', desc: '', args: []);
  }

  /// `Code`
  String get code {
    return Intl.message('Code', name: 'code', desc: '', args: []);
  }

  /// `Is Percentage`
  String get isPercentage {
    return Intl.message(
      'Is Percentage',
      name: 'isPercentage',
      desc: '',
      args: [],
    );
  }

  /// `Show In List`
  String get showInList {
    return Intl.message('Show In List', name: 'showInList', desc: '', args: []);
  }

  /// `Add Promo Code`
  String get addPromoCode {
    return Intl.message(
      'Add Promo Code',
      name: 'addPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Edit Promo Code`
  String get editPromoCode {
    return Intl.message(
      'Edit Promo Code',
      name: 'editPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Delete Promo Code`
  String get deletePromoCode {
    return Intl.message(
      'Delete Promo Code',
      name: 'deletePromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this promo code?`
  String get deletePromoCodeConfirmationMessage {
    return Intl.message(
      'Are you sure you want to delete this promo code?',
      name: 'deletePromoCodeConfirmationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Promo Codes`
  String get promoCodes {
    return Intl.message('Promo Codes', name: 'promoCodes', desc: '', args: []);
  }

  /// `No Promo Codes`
  String get noPromoCodes {
    return Intl.message(
      'No Promo Codes',
      name: 'noPromoCodes',
      desc: '',
      args: [],
    );
  }

  /// `If you enable this, customers will see the promo code in the app or website promo list`
  String
  get ifYouEnableThisCustomersWillSeeThePromoCodeInTheAppOrWebsitePromoList {
    return Intl.message(
      'If you enable this, customers will see the promo code in the app or website promo list',
      name:
          'ifYouEnableThisCustomersWillSeeThePromoCodeInTheAppOrWebsitePromoList',
      desc: '',
      args: [],
    );
  }

  /// `Promo code already exist`
  String get promoCodeAlreadyExist {
    return Intl.message(
      'Promo code already exist',
      name: 'promoCodeAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this promo code?`
  String get areYouSureYouWantToDeleteThisPromoCode {
    return Intl.message(
      'Are you sure you want to delete this promo code?',
      name: 'areYouSureYouWantToDeleteThisPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `You can't change the price of the product because it is set by the size price !`
  String get youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice {
    return Intl.message(
      'You can\'t change the price of the product because it is set by the size price !',
      name: 'youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice',
      desc: '',
      args: [],
    );
  }

  /// `Please add price to your all sizes`
  String get pleaseAddPriceToYourAllSizes {
    return Intl.message(
      'Please add price to your all sizes',
      name: 'pleaseAddPriceToYourAllSizes',
      desc: '',
      args: [],
    );
  }

  /// `Promo Code`
  String get promoCode {
    return Intl.message('Promo Code', name: 'promoCode', desc: '', args: []);
  }

  /// `Store Active`
  String get storeActive {
    return Intl.message(
      'Store Active',
      name: 'storeActive',
      desc: '',
      args: [],
    );
  }

  /// `Minimum Order Cost`
  String get minimumOrderCost {
    return Intl.message(
      'Minimum Order Cost',
      name: 'minimumOrderCost',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message('Back', name: 'back', desc: '', args: []);
  }

  /// `New Orders Alert Bell`
  String get newOrdersAlertBell {
    return Intl.message(
      'New Orders Alert Bell',
      name: 'newOrdersAlertBell',
      desc: '',
      args: [],
    );
  }

  /// `Free Shipping`
  String get freeShipping {
    return Intl.message(
      'Free Shipping',
      name: 'freeShipping',
      desc: '',
      args: [],
    );
  }

  /// `new order`
  String get newOrder {
    return Intl.message('new order', name: 'newOrder', desc: '', args: []);
  }

  /// `You have new order`
  String get youHaveNewOrder {
    return Intl.message(
      'You have new order',
      name: 'youHaveNewOrder',
      desc: '',
      args: [],
    );
  }

  /// `You have new order, please check your latest orders !`
  String get youHaveNewOrderPleaseCheckYourLatestOrders {
    return Intl.message(
      'You have new order, please check your latest orders !',
      name: 'youHaveNewOrderPleaseCheckYourLatestOrders',
      desc: '',
      args: [],
    );
  }

  /// `Exit the app`
  String get exitTheApp {
    return Intl.message('Exit the app', name: 'exitTheApp', desc: '', args: []);
  }

  /// `Are you sure you want to exit the app?`
  String get areYouSureYouWantToExitTheApp {
    return Intl.message(
      'Are you sure you want to exit the app?',
      name: 'areYouSureYouWantToExitTheApp',
      desc: '',
      args: [],
    );
  }

  /// `You may need to re-install the app to apply changes`
  String get youMayNeedToReInstallTheAppToApplyChanges {
    return Intl.message(
      'You may need to re-install the app to apply changes',
      name: 'youMayNeedToReInstallTheAppToApplyChanges',
      desc: '',
      args: [],
    );
  }

  /// `Arabic`
  String get arabic {
    return Intl.message('Arabic', name: 'arabic', desc: '', args: []);
  }

  /// `You must add one of those names`
  String get youMustAddOneOfThoseNames {
    return Intl.message(
      'You must add one of those names',
      name: 'youMustAddOneOfThoseNames',
      desc: '',
      args: [],
    );
  }

  /// `English Name`
  String get englishName {
    return Intl.message(
      'English Name',
      name: 'englishName',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Name`
  String get arabicName {
    return Intl.message('Arabic Name', name: 'arabicName', desc: '', args: []);
  }

  /// `English Description`
  String get englishDescription {
    return Intl.message(
      'English Description',
      name: 'englishDescription',
      desc: '',
      args: [],
    );
  }

  /// `Arabic Description`
  String get arabicDescription {
    return Intl.message(
      'Arabic Description',
      name: 'arabicDescription',
      desc: '',
      args: [],
    );
  }

  /// `You must add at least one description in Arabic or English.`
  String get youMustAddOneOfThoseDescriptions {
    return Intl.message(
      'You must add at least one description in Arabic or English.',
      name: 'youMustAddOneOfThoseDescriptions',
      desc: '',
      args: [],
    );
  }

  /// `You must add at least one title in Arabic or English.`
  String get youMustAddOneOfThoseTitles {
    return Intl.message(
      'You must add at least one title in Arabic or English.',
      name: 'youMustAddOneOfThoseTitles',
      desc: '',
      args: [],
    );
  }

  /// `Size already exist`
  String get sizeAlreadyExist {
    return Intl.message(
      'Size already exist',
      name: 'sizeAlreadyExist',
      desc: '',
      args: [],
    );
  }

  /// `You must add at least one name in Arabic or English.`
  String get youMustAddAtLeastOneName {
    return Intl.message(
      'You must add at least one name in Arabic or English.',
      name: 'youMustAddAtLeastOneName',
      desc: '',
      args: [],
    );
  }

  /// `QR Landing`
  String get qrLanding {
    return Intl.message('QR Landing', name: 'qrLanding', desc: '', args: []);
  }

  /// `QR Type`
  String get qrType {
    return Intl.message('QR Type', name: 'qrType', desc: '', args: []);
  }

  /// `Generate`
  String get generate {
    return Intl.message('Generate', name: 'generate', desc: '', args: []);
  }

  /// `Choose QR Type`
  String get chooseQRType {
    return Intl.message(
      'Choose QR Type',
      name: 'chooseQRType',
      desc: '',
      args: [],
    );
  }

  /// `Please choose QR Type`
  String get pleaseChooseQrType {
    return Intl.message(
      'Please choose QR Type',
      name: 'pleaseChooseQrType',
      desc: '',
      args: [],
    );
  }

  /// `QR`
  String get qr {
    return Intl.message('QR', name: 'qr', desc: '', args: []);
  }

  /// `Share`
  String get share {
    return Intl.message('Share', name: 'share', desc: '', args: []);
  }

  /// `Visit Website`
  String get visitWebsite {
    return Intl.message(
      'Visit Website',
      name: 'visitWebsite',
      desc: '',
      args: [],
    );
  }

  /// `QR Settings`
  String get qrSettings {
    return Intl.message('QR Settings', name: 'qrSettings', desc: '', args: []);
  }

  /// `Cart is empty`
  String get cartIsEmpty {
    return Intl.message(
      'Cart is empty',
      name: 'cartIsEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Show My Logo`
  String get showLogo {
    return Intl.message('Show My Logo', name: 'showLogo', desc: '', args: []);
  }

  /// `Logo Size`
  String get logoSize {
    return Intl.message('Logo Size', name: 'logoSize', desc: '', args: []);
  }

  /// `Large`
  String get large {
    return Intl.message('Large', name: 'large', desc: '', args: []);
  }

  /// `Medium`
  String get medium {
    return Intl.message('Medium', name: 'medium', desc: '', args: []);
  }

  /// `Small`
  String get small {
    return Intl.message('Small', name: 'small', desc: '', args: []);
  }

  /// `Your Youtube Link`
  String get YourYoutubeLink {
    return Intl.message(
      'Your Youtube Link',
      name: 'YourYoutubeLink',
      desc: '',
      args: [],
    );
  }

  /// `Play Store`
  String get playStore {
    return Intl.message('Play Store', name: 'playStore', desc: '', args: []);
  }

  /// `App Store`
  String get appStore {
    return Intl.message('App Store', name: 'appStore', desc: '', args: []);
  }

  /// `Product Stock`
  String get productStock {
    return Intl.message(
      'Product Stock',
      name: 'productStock',
      desc: '',
      args: [],
    );
  }

  /// `Color Stock is off`
  String get colorStockIInventoryIsOff {
    return Intl.message(
      'Color Stock is off',
      name: 'colorStockIInventoryIsOff',
      desc: '',
      args: [],
    );
  }

  /// `Size Stock is off`
  String get sizeStockIInventoryIsOff {
    return Intl.message(
      'Size Stock is off',
      name: 'sizeStockIInventoryIsOff',
      desc: '',
      args: [],
    );
  }

  /// `Manage Sizes & Colors`
  String get manageSizesAndColors {
    return Intl.message(
      'Manage Sizes & Colors',
      name: 'manageSizesAndColors',
      desc: '',
      args: [],
    );
  }

  /// `Stock`
  String get stock {
    return Intl.message('Stock', name: 'stock', desc: '', args: []);
  }

  /// `Product Status`
  String get productStatus {
    return Intl.message(
      'Product Status',
      name: 'productStatus',
      desc: '',
      args: [],
    );
  }

  /// `Primary Color`
  String get primaryColor {
    return Intl.message(
      'Primary Color',
      name: 'primaryColor',
      desc: '',
      args: [],
    );
  }

  /// `Application / Website Settings`
  String get applicationAndWebsiteSettings {
    return Intl.message(
      'Application / Website Settings',
      name: 'applicationAndWebsiteSettings',
      desc: '',
      args: [],
    );
  }

  /// `Orders Settings`
  String get ordersSettings {
    return Intl.message(
      'Orders Settings',
      name: 'ordersSettings',
      desc: '',
      args: [],
    );
  }

  /// `Theme & Language Settings`
  String get themeAndLanguageSettings {
    return Intl.message(
      'Theme & Language Settings',
      name: 'themeAndLanguageSettings',
      desc: '',
      args: [],
    );
  }

  /// `Default Language`
  String get defaultLanguage {
    return Intl.message(
      'Default Language',
      name: 'defaultLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Default Theme`
  String get defaultTheme {
    return Intl.message(
      'Default Theme',
      name: 'defaultTheme',
      desc: '',
      args: [],
    );
  }

  /// `This theme will be the default for your clients in the app or website.`
  String get defaultThemeDescription {
    return Intl.message(
      'This theme will be the default for your clients in the app or website.',
      name: 'defaultThemeDescription',
      desc: '',
      args: [],
    );
  }

  /// `Link`
  String get link {
    return Intl.message('Link', name: 'link', desc: '', args: []);
  }

  /// `QR Square`
  String get qrSquare {
    return Intl.message('QR Square', name: 'qrSquare', desc: '', args: []);
  }

  /// `QR Circle`
  String get qrCircle {
    return Intl.message('QR Circle', name: 'qrCircle', desc: '', args: []);
  }

  /// `Dashboard`
  String get dashboard {
    return Intl.message('Dashboard', name: 'dashboard', desc: '', args: []);
  }

  /// `Main Categories`
  String get mainCategories {
    return Intl.message(
      'Main Categories',
      name: 'mainCategories',
      desc: '',
      args: [],
    );
  }

  /// `Add Main Category`
  String get addMainCategory {
    return Intl.message(
      'Add Main Category',
      name: 'addMainCategory',
      desc: '',
      args: [],
    );
  }

  /// `Edit Main Category`
  String get editMainCategory {
    return Intl.message(
      'Edit Main Category',
      name: 'editMainCategory',
      desc: '',
      args: [],
    );
  }

  /// `This language will be the default for your clients in the app or website.`
  String get defaultLanguageDescription {
    return Intl.message(
      'This language will be the default for your clients in the app or website.',
      name: 'defaultLanguageDescription',
      desc: '',
      args: [],
    );
  }

  /// `you must add stock for the\ncolors or sizes`
  String get youMustAddStockForColorOrSizes {
    return Intl.message(
      'you must add stock for the\ncolors or sizes',
      name: 'youMustAddStockForColorOrSizes',
      desc: '',
      args: [],
    );
  }

  /// `Sort Products`
  String get sortProducts {
    return Intl.message(
      'Sort Products',
      name: 'sortProducts',
      desc: '',
      args: [],
    );
  }

  /// `Drag and drop products to change their order. Arrange them the way you prefer.`
  String get sortProductsDescription {
    return Intl.message(
      'Drag and drop products to change their order. Arrange them the way you prefer.',
      name: 'sortProductsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Edit Prices`
  String get bulkEdit {
    return Intl.message('Edit Prices', name: 'bulkEdit', desc: '', args: []);
  }

  /// `edit products prices`
  String get bulkEditProducts {
    return Intl.message(
      'edit products prices',
      name: 'bulkEditProducts',
      desc: '',
      args: [],
    );
  }

  /// `Select Products`
  String get selectProducts {
    return Intl.message(
      'Select Products',
      name: 'selectProducts',
      desc: '',
      args: [],
    );
  }

  /// `Selected Products`
  String get selectedProducts {
    return Intl.message(
      'Selected Products',
      name: 'selectedProducts',
      desc: '',
      args: [],
    );
  }

  /// `Update Selected`
  String get updateSelected {
    return Intl.message(
      'Update Selected',
      name: 'updateSelected',
      desc: '',
      args: [],
    );
  }

  /// `Sort Main Categories`
  String get sortMainCategories {
    return Intl.message(
      'Sort Main Categories',
      name: 'sortMainCategories',
      desc: '',
      args: [],
    );
  }

  /// `Drag and drop main categories to change their order. Arrange them the way you prefer.`
  String get sortMainCategoriesDescription {
    return Intl.message(
      'Drag and drop main categories to change their order. Arrange them the way you prefer.',
      name: 'sortMainCategoriesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Categories`
  String get categoriesTab {
    return Intl.message(
      'Categories',
      name: 'categoriesTab',
      desc: '',
      args: [],
    );
  }

  /// `Main Categories`
  String get mainCategoriesTab {
    return Intl.message(
      'Main Categories',
      name: 'mainCategoriesTab',
      desc: '',
      args: [],
    );
  }

  /// `more`
  String get more {
    return Intl.message('more', name: 'more', desc: '', args: []);
  }

  /// `No changes to save`
  String get noChangesToSave {
    return Intl.message(
      'No changes to save',
      name: 'noChangesToSave',
      desc: '',
      args: [],
    );
  }

  /// `Bulk edit saved successfully`
  String get bulkEditSavedSuccessfully {
    return Intl.message(
      'Bulk edit saved successfully',
      name: 'bulkEditSavedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Size Pricing`
  String get sizePricing {
    return Intl.message(
      'Size Pricing',
      name: 'sizePricing',
      desc: '',
      args: [],
    );
  }

  /// `Sale price should be less than regular price`
  String get salePriceShouldBeLessThanPrice {
    return Intl.message(
      'Sale price should be less than regular price',
      name: 'salePriceShouldBeLessThanPrice',
      desc: '',
      args: [],
    );
  }

  /// `If you don't want to add stock for them, you can choose product stock or turn off the inventory`
  String
  get IfYouDontWantToAddStockForThemYouCanChooseProductStockOrTurnOffTheInventory {
    return Intl.message(
      'If you don\'t want to add stock for them, you can choose product stock or turn off the inventory',
      name:
          'IfYouDontWantToAddStockForThemYouCanChooseProductStockOrTurnOffTheInventory',
      desc: '',
      args: [],
    );
  }

  /// `No sub-categories available`
  String get noSubCategories {
    return Intl.message(
      'No sub-categories available',
      name: 'noSubCategories',
      desc: '',
      args: [],
    );
  }

  /// `No Sub Categories`
  String get noSelectedSubCategories {
    return Intl.message(
      'No Sub Categories',
      name: 'noSelectedSubCategories',
      desc: '',
      args: [],
    );
  }

  /// `Areas In {cityName}`
  String areasIn(Object cityName) {
    return Intl.message(
      'Areas In $cityName',
      name: 'areasIn',
      desc: '',
      args: [cityName],
    );
  }

  /// `No Areas Found`
  String get noAreasFound {
    return Intl.message(
      'No Areas Found',
      name: 'noAreasFound',
      desc: 'Message shown when no areas are found in search',
      args: [],
    );
  }

  /// `Areas`
  String get areas {
    return Intl.message('Areas', name: 'areas', desc: '', args: []);
  }

  /// `Sort Categories`
  String get sortCategories {
    return Intl.message(
      'Sort Categories',
      name: 'sortCategories',
      desc: '',
      args: [],
    );
  }

  /// `Hold and drag a category to change its position. Arrange them the way you prefer.`
  String get sortCategoriesDescription {
    return Intl.message(
      'Hold and drag a category to change its position. Arrange them the way you prefer.',
      name: 'sortCategoriesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Cost`
  String get cost {
    return Intl.message('Cost', name: 'cost', desc: '', args: []);
  }

  /// `Add Area`
  String get addArea {
    return Intl.message('Add Area', name: 'addArea', desc: '', args: []);
  }

  /// `Suggestions`
  String get suggestions {
    return Intl.message('Suggestions', name: 'suggestions', desc: '', args: []);
  }

  /// `Suggestions Areas`
  String get suggestionsAreas {
    return Intl.message(
      'Suggestions Areas',
      name: 'suggestionsAreas',
      desc: '',
      args: [],
    );
  }

  /// `Select All`
  String get selectAll {
    return Intl.message('Select All', name: 'selectAll', desc: '', args: []);
  }

  /// `Select Areas`
  String get selectAreas {
    return Intl.message(
      'Select Areas',
      name: 'selectAreas',
      desc: '',
      args: [],
    );
  }

  /// `Selected Areas`
  String get selectedAreas {
    return Intl.message(
      'Selected Areas',
      name: 'selectedAreas',
      desc: '',
      args: [],
    );
  }

  /// `You must add cost for all areas you selected`
  String get youMustButCostForAllAreasYouSelected {
    return Intl.message(
      'You must add cost for all areas you selected',
      name: 'youMustButCostForAllAreasYouSelected',
      desc: '',
      args: [],
    );
  }

  /// `Share Link`
  String get shareLink {
    return Intl.message('Share Link', name: 'shareLink', desc: '', args: []);
  }

  /// `Share QR`
  String get shareQr {
    return Intl.message('Share QR', name: 'shareQr', desc: '', args: []);
  }

  /// `Latest Orders`
  String get latestOrders {
    return Intl.message(
      'Latest Orders',
      name: 'latestOrders',
      desc: '',
      args: [],
    );
  }

  /// `Free Plan`
  String get freePlan {
    return Intl.message('Free Plan', name: 'freePlan', desc: '', args: []);
  }

  /// `Delete Category`
  String get deleteCategory {
    return Intl.message(
      'Delete Category',
      name: 'deleteCategory',
      desc: '',
      args: [],
    );
  }

  /// `AI Assistant`
  String get aiAssistant {
    return Intl.message(
      'AI Assistant',
      name: 'aiAssistant',
      desc: '',
      args: [],
    );
  }

  /// `Categories Settings`
  String get categoriesSettings {
    return Intl.message(
      'Categories Settings',
      name: 'categoriesSettings',
      desc: '',
      args: [],
    );
  }

  /// `Main Category`
  String get mainCategory {
    return Intl.message(
      'Main Category',
      name: 'mainCategory',
      desc: '',
      args: [],
    );
  }

  /// `Create a main category to group related subcategories under one parent. For example: a 'Men' category can include 'Jackets', 'T-Shirts', and 'Pants'.`
  String get mainCategorySettingsDesc {
    return Intl.message(
      'Create a main category to group related subcategories under one parent. For example: a \'Men\' category can include \'Jackets\', \'T-Shirts\', and \'Pants\'.',
      name: 'mainCategorySettingsDesc',
      desc: '',
      args: [],
    );
  }

  /// `Categories Without Main Category`
  String get categoriesWithoutMainCategory {
    return Intl.message(
      'Categories Without Main Category',
      name: 'categoriesWithoutMainCategory',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this main category {mainCategoryName} ?`
  String areYouSureYouWantToDeleteThisMainCategory(Object mainCategoryName) {
    return Intl.message(
      'Are you sure you want to delete this main category $mainCategoryName ?',
      name: 'areYouSureYouWantToDeleteThisMainCategory',
      desc: '',
      args: [mainCategoryName],
    );
  }

  /// `Total\nEarnings`
  String get totalProfit {
    return Intl.message(
      'Total\nEarnings',
      name: 'totalProfit',
      desc: '',
      args: [],
    );
  }

  /// `Daily\nOrders`
  String get dailyOrders {
    return Intl.message(
      'Daily\nOrders',
      name: 'dailyOrders',
      desc: '',
      args: [],
    );
  }

  /// `Daily\nEarnings`
  String get dailyProfit {
    return Intl.message(
      'Daily\nEarnings',
      name: 'dailyProfit',
      desc: '',
      args: [],
    );
  }

  /// `Total\nOrders`
  String get totalOrdersHome {
    return Intl.message(
      'Total\nOrders',
      name: 'totalOrdersHome',
      desc: '',
      args: [],
    );
  }

  /// `App & Website Settings`
  String get appAndWebsiteSettings {
    return Intl.message(
      'App & Website Settings',
      name: 'appAndWebsiteSettings',
      desc: '',
      args: [],
    );
  }

  /// `Orders & Shipping`
  String get ordersAndShipping {
    return Intl.message(
      'Orders & Shipping',
      name: 'ordersAndShipping',
      desc: '',
      args: [],
    );
  }

  /// `Store Appearance`
  String get storeAppearance {
    return Intl.message(
      'Store Appearance',
      name: 'storeAppearance',
      desc: '',
      args: [],
    );
  }

  /// `Payments & Promotions`
  String get paymentsAndPromotions {
    return Intl.message(
      'Payments & Promotions',
      name: 'paymentsAndPromotions',
      desc: '',
      args: [],
    );
  }

  /// `Reports & Operations`
  String get reportsAndOperations {
    return Intl.message(
      'Reports & Operations',
      name: 'reportsAndOperations',
      desc: '',
      args: [],
    );
  }

  /// `Show Details`
  String get showDetails {
    return Intl.message(
      'Show Details',
      name: 'showDetails',
      desc: '',
      args: [],
    );
  }

  /// `Hide`
  String get hide {
    return Intl.message('Hide', name: 'hide', desc: '', args: []);
  }

  /// `Users Orders`
  String get usersOrders {
    return Intl.message(
      'Users Orders',
      name: 'usersOrders',
      desc: '',
      args: [],
    );
  }

  /// `Delete Sale`
  String get deleteSale {
    return Intl.message('Delete Sale', name: 'deleteSale', desc: '', args: []);
  }

  /// `No Users Orders`
  String get noUsersOrders {
    return Intl.message(
      'No Users Orders',
      name: 'noUsersOrders',
      desc: '',
      args: [],
    );
  }

  /// `Failed to send notifications to users`
  String get failedToSendNotificationsToUsers {
    return Intl.message(
      'Failed to send notifications to users',
      name: 'failedToSendNotificationsToUsers',
      desc: '',
      args: [],
    );
  }

  /// `Pay Attachment`
  String get PayAttachment {
    return Intl.message(
      'Pay Attachment',
      name: 'PayAttachment',
      desc: '',
      args: [],
    );
  }

  /// `Invoice`
  String get invoice {
    return Intl.message('Invoice', name: 'invoice', desc: '', args: []);
  }

  /// `Attachment`
  String get attachment {
    return Intl.message('Attachment', name: 'attachment', desc: '', args: []);
  }

  /// `This product is deleted`
  String get thisProductIsDeleted {
    return Intl.message(
      'This product is deleted',
      name: 'thisProductIsDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited`
  String get unlimited {
    return Intl.message('Unlimited', name: 'unlimited', desc: '', args: []);
  }

  /// `Choose Categories Type\nYou Want To Sort`
  String get chooseCategoriesTypeYouWantToSort {
    return Intl.message(
      'Choose Categories Type\nYou Want To Sort',
      name: 'chooseCategoriesTypeYouWantToSort',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this area?`
  String get areYouSureYouWantToDeleteThisArea {
    return Intl.message(
      'Are you sure you want to delete this area?',
      name: 'areYouSureYouWantToDeleteThisArea',
      desc: '',
      args: [],
    );
  }

  /// `(Note: You can change to a custom domain anytime)`
  String get changeWebsiteLater {
    return Intl.message(
      '(Note: You can change to a custom domain anytime)',
      name: 'changeWebsiteLater',
      desc: '',
      args: [],
    );
  }

  /// `Update Available`
  String get updateAvailable {
    return Intl.message(
      'Update Available',
      name: 'updateAvailable',
      desc: '',
      args: [],
    );
  }

  /// `A new version of the app is available. Please update the app to continue using it.`
  String get updateAvailableDesc {
    return Intl.message(
      'A new version of the app is available. Please update the app to continue using it.',
      name: 'updateAvailableDesc',
      desc: '',
      args: [],
    );
  }

  /// `Update Now`
  String get updateNow {
    return Intl.message('Update Now', name: 'updateNow', desc: '', args: []);
  }

  /// `Global Sale`
  String get globalOffer {
    return Intl.message('Global Sale', name: 'globalOffer', desc: '', args: []);
  }

  /// `Sale`
  String get sale {
    return Intl.message('Sale', name: 'sale', desc: '', args: []);
  }

  /// `Sale Quantity Price`
  String get saleQuantityPrice {
    return Intl.message(
      'Sale Quantity Price',
      name: 'saleQuantityPrice',
      desc: '',
      args: [],
    );
  }

  /// `Set how many items the customer must buy to get the sale price`
  String get saleQuantityPriceDesc {
    return Intl.message(
      'Set how many items the customer must buy to get the sale price',
      name: 'saleQuantityPriceDesc',
      desc: '',
      args: [],
    );
  }

  /// `It is a general Sale that is applied to all products in the store`
  String get globalOfferDec {
    return Intl.message(
      'It is a general Sale that is applied to all products in the store',
      name: 'globalOfferDec',
      desc: '',
      args: [],
    );
  }

  /// `You’ve set a {discount} global discount for all your products.`
  String globalOfferDecRimender(Object discount) {
    return Intl.message(
      'You’ve set a $discount global discount for all your products.',
      name: 'globalOfferDecRimender',
      desc: '',
      args: [discount],
    );
  }

  /// `Your global discount made some prices negative. Please adjust it.`
  String get globalDiscountError {
    return Intl.message(
      'Your global discount made some prices negative. Please adjust it.',
      name: 'globalDiscountError',
      desc: '',
      args: [],
    );
  }

  /// `The Percentage should be less than 100%`
  String get editGlobalOfferError {
    return Intl.message(
      'The Percentage should be less than 100%',
      name: 'editGlobalOfferError',
      desc: '',
      args: [],
    );
  }

  /// `Please Add Sizes`
  String get pleaseAddSizes {
    return Intl.message(
      'Please Add Sizes',
      name: 'pleaseAddSizes',
      desc: '',
      args: [],
    );
  }

  /// `Delete Offer`
  String get deleteOffer {
    return Intl.message(
      'Delete Offer',
      name: 'deleteOffer',
      desc: '',
      args: [],
    );
  }

  /// `You must add min quantity sale number and quantity sale`
  String get youMustAddMinQuantitySaleNumberAndQuantitySale {
    return Intl.message(
      'You must add min quantity sale number and quantity sale',
      name: 'youMustAddMinQuantitySaleNumberAndQuantitySale',
      desc: '',
      args: [],
    );
  }

  /// `Select Currency`
  String get selectCurrency {
    return Intl.message(
      'Select Currency',
      name: 'selectCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Select Your Currency For The Apps\nYou Can Change It Latter`
  String get selectYourCurrencyForTheAppsYouCanChangedLatter {
    return Intl.message(
      'Select Your Currency For The Apps\nYou Can Change It Latter',
      name: 'selectYourCurrencyForTheAppsYouCanChangedLatter',
      desc: '',
      args: [],
    );
  }

  /// `Select Country`
  String get selectCountry {
    return Intl.message(
      'Select Country',
      name: 'selectCountry',
      desc: '',
      args: [],
    );
  }

  /// `Select Your Country For The Apps\nYou Can Change It Latter`
  String get selectYourCountryForTheAppsYouCanChangedLatter {
    return Intl.message(
      'Select Your Country For The Apps\nYou Can Change It Latter',
      name: 'selectYourCountryForTheAppsYouCanChangedLatter',
      desc: '',
      args: [],
    );
  }

  /// `Add & Manage Employees`
  String get addAndManageEmployees {
    return Intl.message(
      'Add & Manage Employees',
      name: 'addAndManageEmployees',
      desc: '',
      args: [],
    );
  }

  /// `Invite employees to your store and set\ntheir access permissions easily`
  String get inviteEmployeesToYourStore {
    return Intl.message(
      'Invite employees to your store and set\ntheir access permissions easily',
      name: 'inviteEmployeesToYourStore',
      desc: '',
      args: [],
    );
  }

  /// `Add Employee`
  String get addEmployee {
    return Intl.message(
      'Add Employee',
      name: 'addEmployee',
      desc: '',
      args: [],
    );
  }

  /// `Permissions`
  String get permissions {
    return Intl.message('Permissions', name: 'permissions', desc: '', args: []);
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `View Orders`
  String get viewOrders {
    return Intl.message('View Orders', name: 'viewOrders', desc: '', args: []);
  }

  /// `Manage Orders`
  String get manageOrders {
    return Intl.message(
      'Manage Orders',
      name: 'manageOrders',
      desc: '',
      args: [],
    );
  }

  /// `View Categories & Products`
  String get viewCategoriesProducts {
    return Intl.message(
      'View Categories & Products',
      name: 'viewCategoriesProducts',
      desc: '',
      args: [],
    );
  }

  /// `Manage Categories & Products`
  String get manageCategoriesProducts {
    return Intl.message(
      'Manage Categories & Products',
      name: 'manageCategoriesProducts',
      desc: '',
      args: [],
    );
  }

  /// `Create Store Orders`
  String get createStoreOrders {
    return Intl.message(
      'Create Store Orders',
      name: 'createStoreOrders',
      desc: '',
      args: [],
    );
  }

  /// `Manage Store Orders`
  String get manageStoreOrders {
    return Intl.message(
      'Manage Store Orders',
      name: 'manageStoreOrders',
      desc: '',
      args: [],
    );
  }

  /// `Access App & Website Settings`
  String get accessAppSettings {
    return Intl.message(
      'Access App & Website Settings',
      name: 'accessAppSettings',
      desc: '',
      args: [],
    );
  }

  /// `Access Orders & Shipping`
  String get accessOrdersShipping {
    return Intl.message(
      'Access Orders & Shipping',
      name: 'accessOrdersShipping',
      desc: '',
      args: [],
    );
  }

  /// `Access Store Appearance`
  String get accessStoreAppearance {
    return Intl.message(
      'Access Store Appearance',
      name: 'accessStoreAppearance',
      desc: '',
      args: [],
    );
  }

  /// `Access Payments & Promotions`
  String get accessPaymentsPromotions {
    return Intl.message(
      'Access Payments & Promotions',
      name: 'accessPaymentsPromotions',
      desc: '',
      args: [],
    );
  }

  /// `Access Users`
  String get accessUsers {
    return Intl.message(
      'Access Users',
      name: 'accessUsers',
      desc: '',
      args: [],
    );
  }

  /// `Access Reports & Operations`
  String get accessReportsOperations {
    return Intl.message(
      'Access Reports & Operations',
      name: 'accessReportsOperations',
      desc: '',
      args: [],
    );
  }

  /// `All Roles`
  String get allRoles {
    return Intl.message('All Roles', name: 'allRoles', desc: '', args: []);
  }

  /// `Products & Categories`
  String get productsAndCategories {
    return Intl.message(
      'Products & Categories',
      name: 'productsAndCategories',
      desc: '',
      args: [],
    );
  }

  /// `your-website-name`
  String get yourWebsiteName {
    return Intl.message(
      'your-website-name',
      name: 'yourWebsiteName',
      desc: '',
      args: [],
    );
  }

  /// `Auto-generated from name`
  String get autoGeneratedFromName {
    return Intl.message(
      'Auto-generated from name',
      name: 'autoGeneratedFromName',
      desc: '',
      args: [],
    );
  }

  /// `Search Employees`
  String get searchEmployees {
    return Intl.message(
      'Search Employees',
      name: 'searchEmployees',
      desc: '',
      args: [],
    );
  }

  /// `Search by name or email`
  String get searchByNameOrEmail {
    return Intl.message(
      'Search by name or email',
      name: 'searchByNameOrEmail',
      desc: '',
      args: [],
    );
  }

  /// `Delete Employee`
  String get deleteEmployee {
    return Intl.message(
      'Delete Employee',
      name: 'deleteEmployee',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this employee?`
  String get areYouSureDeleteEmployee {
    return Intl.message(
      'Are you sure you want to delete this employee?',
      name: 'areYouSureDeleteEmployee',
      desc: '',
      args: [],
    );
  }

  /// `No Name`
  String get noName {
    return Intl.message('No Name', name: 'noName', desc: '', args: []);
  }

  /// `No Email`
  String get noEmail {
    return Intl.message('No Email', name: 'noEmail', desc: '', args: []);
  }

  /// `Inactive`
  String get inactive {
    return Intl.message('Inactive', name: 'inactive', desc: '', args: []);
  }

  /// `roles`
  String get roles {
    return Intl.message('roles', name: 'roles', desc: '', args: []);
  }

  /// `Please select at least one role`
  String get pleaseSelectAtLeastOneRole {
    return Intl.message(
      'Please select at least one role',
      name: 'pleaseSelectAtLeastOneRole',
      desc: '',
      args: [],
    );
  }

  /// `Edit Basic Info`
  String get editBasicInfo {
    return Intl.message(
      'Edit Basic Info',
      name: 'editBasicInfo',
      desc: '',
      args: [],
    );
  }

  /// `Edit Permissions`
  String get editPermissions {
    return Intl.message(
      'Edit Permissions',
      name: 'editPermissions',
      desc: '',
      args: [],
    );
  }

  /// `Edit Employee`
  String get editEmployee {
    return Intl.message(
      'Edit Employee',
      name: 'editEmployee',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message('Status', name: 'status', desc: '', args: []);
  }

  /// `Manage Employees`
  String get manageEmployees {
    return Intl.message(
      'Manage Employees',
      name: 'manageEmployees',
      desc: '',
      args: [],
    );
  }

  /// `No Access to`
  String get noAccessTo {
    return Intl.message('No Access to', name: 'noAccessTo', desc: '', args: []);
  }

  /// `Contact your administrator for access`
  String get contactAdminForAccess {
    return Intl.message(
      'Contact your administrator for access',
      name: 'contactAdminForAccess',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Plan`
  String get upgradePlan {
    return Intl.message(
      'Upgrade Plan',
      name: 'upgradePlan',
      desc: '',
      args: [],
    );
  }

  /// `Contact Support`
  String get contactSupport {
    return Intl.message(
      'Contact Support',
      name: 'contactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Dashboard Tutorial`
  String get dashboardTutorial {
    return Intl.message(
      'Dashboard Tutorial',
      name: 'dashboardTutorial',
      desc: '',
      args: [],
    );
  }

  /// `Customized Description`
  String get customized_description {
    return Intl.message(
      'Customized Description',
      name: 'customized_description',
      desc: '',
      args: [],
    );
  }

  /// `Rich Text Editor`
  String get rich_text_editor {
    return Intl.message(
      'Rich Text Editor',
      name: 'rich_text_editor',
      desc: '',
      args: [],
    );
  }

  /// `Preview`
  String get preview {
    return Intl.message('Preview', name: 'preview', desc: '', args: []);
  }

  /// `Quick Templates`
  String get quick_templates {
    return Intl.message(
      'Quick Templates',
      name: 'quick_templates',
      desc: '',
      args: [],
    );
  }

  /// `Vegetables List`
  String get vegetables_list {
    return Intl.message(
      'Vegetables List',
      name: 'vegetables_list',
      desc: '',
      args: [],
    );
  }

  /// `Fruits List`
  String get fruits_list {
    return Intl.message('Fruits List', name: 'fruits_list', desc: '', args: []);
  }

  /// `Product Features`
  String get product_features {
    return Intl.message(
      'Product Features',
      name: 'product_features',
      desc: '',
      args: [],
    );
  }

  /// `General Settings`
  String get general_settings {
    return Intl.message(
      'General Settings',
      name: 'general_settings',
      desc: '',
      args: [],
    );
  }

  /// `Enter your content here...`
  String get enter_your_content_here {
    return Intl.message(
      'Enter your content here...',
      name: 'enter_your_content_here',
      desc: '',
      args: [],
    );
  }

  /// `Insert Template`
  String get insert_template {
    return Intl.message(
      'Insert Template',
      name: 'insert_template',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
