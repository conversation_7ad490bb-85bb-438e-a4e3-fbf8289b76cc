// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(mainCategoryName) =>
      "Are you sure you want to delete this main category ${mainCategoryName} ?";

  static String m1(cityName) => "Areas In ${cityName}";

  static String m2(cost) => "Delivery cost changed to ${cost}";

  static String m3(discount) =>
      "You’ve set a ${discount} global discount for all your products.";

  static String m5(id) => "Order: #${id} has been canceled";

  static String m4(status) => "Order status changed to ${status}";

  static String m6(name) => "The conversation with #${name} is dismissed";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "ContactSupport": MessageLookupByLibrary.simpleMessage("Contact Support "),
    "IfYouDontWantToAddStockForThemYouCanChooseProductStockOrTurnOffTheInventory":
        MessageLookupByLibrary.simpleMessage(
          "If you don\'t want to add stock for them, you can choose product stock or turn off the inventory",
        ),
    "Or": MessageLookupByLibrary.simpleMessage("Or"),
    "PayAttachment": MessageLookupByLibrary.simpleMessage("Pay Attachment"),
    "Price": MessageLookupByLibrary.simpleMessage("Price"),
    "ToActivateYourOnlinePayment": MessageLookupByLibrary.simpleMessage(
      "To activate your online payment",
    ),
    "YourFacebookLink": MessageLookupByLibrary.simpleMessage(
      "Your Facebook Link",
    ),
    "YourInstagramLink": MessageLookupByLibrary.simpleMessage(
      "Your Instagram Link",
    ),
    "YourTiktokLink": MessageLookupByLibrary.simpleMessage("Your Tiktok Link"),
    "YourWhatsApp": MessageLookupByLibrary.simpleMessage(
      "Your WhatsApp Number",
    ),
    "YourYoutubeLink": MessageLookupByLibrary.simpleMessage(
      "Your Youtube Link",
    ),
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "aboutAndPrivacy": MessageLookupByLibrary.simpleMessage("About & Privacy"),
    "aboutDescription": MessageLookupByLibrary.simpleMessage(
      "As a tech company, we specialize in software solutions, offering a diverse range of products and expertise in developing bespoke applications. Our team is dedicated to crafting innovative software products, and we have the capability to create tailored software apps designed to meet your specific requirements. In addition, we offer a cutting-edge vendor app that empowers you to efficiently manage your business. We are committed to enhancing your business through our advanced technology solutions.",
    ),
    "aboutUs": MessageLookupByLibrary.simpleMessage("About us"),
    "accessAppSettings": MessageLookupByLibrary.simpleMessage(
      "Access App & Website Settings",
    ),
    "accessOrdersShipping": MessageLookupByLibrary.simpleMessage(
      "Access Orders & Shipping",
    ),
    "accessPaymentsPromotions": MessageLookupByLibrary.simpleMessage(
      "Access Payments & Promotions",
    ),
    "accessReportsOperations": MessageLookupByLibrary.simpleMessage(
      "Access Reports & Operations",
    ),
    "accessStoreAppearance": MessageLookupByLibrary.simpleMessage(
      "Access Store Appearance",
    ),
    "accessUsers": MessageLookupByLibrary.simpleMessage("Access Users"),
    "accountSettings": MessageLookupByLibrary.simpleMessage("Account Settings"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addAndManageEmployees": MessageLookupByLibrary.simpleMessage(
      "Add & Manage Employees",
    ),
    "addArea": MessageLookupByLibrary.simpleMessage("Add Area"),
    "addBanner": MessageLookupByLibrary.simpleMessage("Add Banner"),
    "addCategory": MessageLookupByLibrary.simpleMessage("Add category"),
    "addColors": MessageLookupByLibrary.simpleMessage("Add Colors"),
    "addColorsStock": MessageLookupByLibrary.simpleMessage("Add Colors Stock"),
    "addEmployee": MessageLookupByLibrary.simpleMessage("Add Employee"),
    "addExpenses": MessageLookupByLibrary.simpleMessage("Add Expenses"),
    "addMainCategory": MessageLookupByLibrary.simpleMessage(
      "Add Main Category",
    ),
    "addNewVendor": MessageLookupByLibrary.simpleMessage("Add New Vendor"),
    "addPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Add Payment Method",
    ),
    "addProduct": MessageLookupByLibrary.simpleMessage("Add Product"),
    "addPromoCode": MessageLookupByLibrary.simpleMessage("Add Promo Code"),
    "addSizes": MessageLookupByLibrary.simpleMessage("Add Sizes"),
    "addSizesStock": MessageLookupByLibrary.simpleMessage("Add Sizes Stock"),
    "addStocks": MessageLookupByLibrary.simpleMessage("Add Stock"),
    "addTask": MessageLookupByLibrary.simpleMessage("Add Task"),
    "addToCart": MessageLookupByLibrary.simpleMessage("Add To Cart"),
    "addYourFirstBanner": MessageLookupByLibrary.simpleMessage(
      "Add your first banner",
    ),
    "addYourFirstCategory": MessageLookupByLibrary.simpleMessage(
      "Add your first category",
    ),
    "addYourFirstProduct": MessageLookupByLibrary.simpleMessage(
      "Add your first product",
    ),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Added successfully",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "address_not_provided_contact_client": MessageLookupByLibrary.simpleMessage(
      "Address not provided contact client",
    ),
    "address_not_provided_please_call_the_client":
        MessageLookupByLibrary.simpleMessage(
          "Address not provided please call the client",
        ),
    "addresses_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Addresses refreshed successfuly",
    ),
    "ago": MessageLookupByLibrary.simpleMessage("ago"),
    "aiAssistant": MessageLookupByLibrary.simpleMessage("AI Assistant"),
    "aiHintMessage": MessageLookupByLibrary.simpleMessage("Ask me anything..."),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "allRoles": MessageLookupByLibrary.simpleMessage("All Roles"),
    "annually": MessageLookupByLibrary.simpleMessage("Annually"),
    "apartment": MessageLookupByLibrary.simpleMessage("Apartment"),
    "appAndWebsiteSettings": MessageLookupByLibrary.simpleMessage(
      "App & Website Settings",
    ),
    "appSettings": MessageLookupByLibrary.simpleMessage("App Settings"),
    "appStore": MessageLookupByLibrary.simpleMessage("App Store"),
    "app_language": MessageLookupByLibrary.simpleMessage("App Language"),
    "app_settings": MessageLookupByLibrary.simpleMessage("App Settings"),
    "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
    "applicationAndWebsiteSettings": MessageLookupByLibrary.simpleMessage(
      "Application / Website Settings",
    ),
    "application_preferences": MessageLookupByLibrary.simpleMessage(
      "Application Preferences",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "approve": MessageLookupByLibrary.simpleMessage("Approve"),
    "approveRequest": MessageLookupByLibrary.simpleMessage("Approve Request"),
    "approveSubscriptionRequest": MessageLookupByLibrary.simpleMessage(
      "Approve Subscription Request",
    ),
    "approved": MessageLookupByLibrary.simpleMessage("Approved"),
    "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "arabicDescription": MessageLookupByLibrary.simpleMessage(
      "Arabic Description",
    ),
    "arabicName": MessageLookupByLibrary.simpleMessage("Arabic Name"),
    "areYouSureDeleteEmployee": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this employee?",
    ),
    "areYouSureYouWantToApproveThisSubscriptionRequest":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to approve this subscription request?",
        ),
    "areYouSureYouWantToCancelThisOrderOf":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to cancel this order of customer ?",
        ),
    "areYouSureYouWantToDeleteThisArea": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this area?",
    ),
    "areYouSureYouWantToDeleteThisColor": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this color?",
    ),
    "areYouSureYouWantToDeleteThisExpense":
        MessageLookupByLibrary.simpleMessage(
          "Are You Sure You Want To Delete This Expense ?",
        ),
    "areYouSureYouWantToDeleteThisMainCategory": m0,
    "areYouSureYouWantToDeleteThisPayment":
        MessageLookupByLibrary.simpleMessage(
          "Are You Sure You Want To Delete This Payment Method ?",
        ),
    "areYouSureYouWantToDeleteThisPromoCode":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this promo code?",
        ),
    "areYouSureYouWantToDeleteThisSize": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this size?",
    ),
    "areYouSureYouWantToDeleteThisSubscriptionRequest":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this subscription request?",
        ),
    "areYouSureYouWantToDeleteThisTask": MessageLookupByLibrary.simpleMessage(
      "Are You Sure You Want To Delete This Task ?",
    ),
    "areYouSureYouWantToDeleteThisVendor": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this vendor?",
    ),
    "areYouSureYouWantToDeleteYourAccount":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete your account ?",
        ),
    "areYouSureYouWantToExitTheApp": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to exit the app?",
    ),
    "area": MessageLookupByLibrary.simpleMessage("Area"),
    "areas": MessageLookupByLibrary.simpleMessage("Areas"),
    "areasIn": m1,
    "askMeAnything": MessageLookupByLibrary.simpleMessage(
      "Ask me anything\n(Marketing, Sales, Tech, etc...)",
    ),
    "assignDeliveryBoy": MessageLookupByLibrary.simpleMessage(
      "Assign Delivery Boy",
    ),
    "attachment": MessageLookupByLibrary.simpleMessage("Attachment"),
    "autoGeneratedFromName": MessageLookupByLibrary.simpleMessage(
      "Auto-generated from name",
    ),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "bankAccount": MessageLookupByLibrary.simpleMessage("Bank Account"),
    "bannerLimitReached": MessageLookupByLibrary.simpleMessage(
      "Banner limit reached !",
    ),
    "banners": MessageLookupByLibrary.simpleMessage("Banners"),
    "bestImageSize": MessageLookupByLibrary.simpleMessage(
      "3000W X 1200H is a great size for Banner",
    ),
    "building": MessageLookupByLibrary.simpleMessage("Building"),
    "bulkEdit": MessageLookupByLibrary.simpleMessage("Edit Prices"),
    "bulkEditProducts": MessageLookupByLibrary.simpleMessage(
      "edit products prices",
    ),
    "bulkEditSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Bulk edit saved successfully",
    ),
    "businessName": MessageLookupByLibrary.simpleMessage("Business Name"),
    "businessType": MessageLookupByLibrary.simpleMessage("Business Type"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "canceled": MessageLookupByLibrary.simpleMessage("Canceled"),
    "cantChangeStatus": MessageLookupByLibrary.simpleMessage(
      "You can\'t change the status",
    ),
    "cantConnectToServer": MessageLookupByLibrary.simpleMessage(
      "Cannot connect to server. Please try again later...",
    ),
    "cart": MessageLookupByLibrary.simpleMessage("Cart"),
    "cartIsEmpty": MessageLookupByLibrary.simpleMessage("Cart is empty"),
    "carts_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Carts refreshed successfully",
    ),
    "cash_on_delivery": MessageLookupByLibrary.simpleMessage(
      "Cash on delivery",
    ),
    "categories": MessageLookupByLibrary.simpleMessage("Categories"),
    "categoriesSettings": MessageLookupByLibrary.simpleMessage(
      "Categories Settings",
    ),
    "categoriesTab": MessageLookupByLibrary.simpleMessage("Categories"),
    "categoriesWithoutMainCategory": MessageLookupByLibrary.simpleMessage(
      "Categories Without Main Category",
    ),
    "category": MessageLookupByLibrary.simpleMessage("Category"),
    "categoryLimitReached": MessageLookupByLibrary.simpleMessage(
      "Limit reached",
    ),
    "categoryLimitReachedDesc": MessageLookupByLibrary.simpleMessage(
      "Upgrade your plan now and unlock unlimited addition, Enjoy full access without restrictions and grow your store seamlessly",
    ),
    "category_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Category refreshed successfully",
    ),
    "changePlan": MessageLookupByLibrary.simpleMessage("Change Plan"),
    "changeWebsiteLater": MessageLookupByLibrary.simpleMessage(
      "(Note: You can change to a custom domain anytime)",
    ),
    "checkedOutSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Checked Out Successfully",
    ),
    "checkout": MessageLookupByLibrary.simpleMessage("Checkout"),
    "checkout_settings": MessageLookupByLibrary.simpleMessage(
      "Checkout Settings",
    ),
    "checkout_type": MessageLookupByLibrary.simpleMessage("Checkout Type"),
    "chooseCategoriesTypeYouWantToSort": MessageLookupByLibrary.simpleMessage(
      "Choose Categories Type\nYou Want To Sort",
    ),
    "choosePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "Choose Payment Methods",
    ),
    "chooseQRType": MessageLookupByLibrary.simpleMessage("Choose QR Type"),
    "chooseYourPlan": MessageLookupByLibrary.simpleMessage("Choose Your Plan"),
    "city": MessageLookupByLibrary.simpleMessage("City"),
    "clearFilter": MessageLookupByLibrary.simpleMessage("Clear Filter"),
    "clickToBrowse": MessageLookupByLibrary.simpleMessage("Click to browse"),
    "clickToBrowseLogo": MessageLookupByLibrary.simpleMessage(
      "Click to browse Logo",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "closed": MessageLookupByLibrary.simpleMessage("Closed"),
    "code": MessageLookupByLibrary.simpleMessage("Code"),
    "color": MessageLookupByLibrary.simpleMessage("Color"),
    "colorAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Color already exist",
    ),
    "colorStockIInventoryIsOff": MessageLookupByLibrary.simpleMessage(
      "Color Stock is off",
    ),
    "colors": MessageLookupByLibrary.simpleMessage("Colors"),
    "colorsAndSizesStock": MessageLookupByLibrary.simpleMessage(
      "Colors & Sizes Stock",
    ),
    "completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmTheChanges": MessageLookupByLibrary.simpleMessage(
      "Would you please confirm if you want to save changes",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("Confirmation"),
    "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
    "contactAdminForAccess": MessageLookupByLibrary.simpleMessage(
      "Contact your administrator for access",
    ),
    "contactSupport": MessageLookupByLibrary.simpleMessage("Contact Support"),
    "contactUs": MessageLookupByLibrary.simpleMessage("Contact us"),
    "copiedToClipboard": MessageLookupByLibrary.simpleMessage(
      "Copied to clipboard",
    ),
    "cost": MessageLookupByLibrary.simpleMessage("Cost"),
    "createStoreOrders": MessageLookupByLibrary.simpleMessage(
      "Create Store Orders",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("Customer"),
    "customerInfo": MessageLookupByLibrary.simpleMessage(
      "Customer Information",
    ),
    "customers": MessageLookupByLibrary.simpleMessage("Customers"),
    "dailyOrders": MessageLookupByLibrary.simpleMessage("Daily\nOrders"),
    "dailyProfit": MessageLookupByLibrary.simpleMessage("Daily\nEarnings"),
    "dark": MessageLookupByLibrary.simpleMessage("Dark"),
    "dark_mode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
    "dashboardTutorial": MessageLookupByLibrary.simpleMessage(
      "Dashboard Tutorial",
    ),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "dateAndTime": MessageLookupByLibrary.simpleMessage("Date & Time"),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "defaultLanguage": MessageLookupByLibrary.simpleMessage("Default Language"),
    "defaultLanguageDescription": MessageLookupByLibrary.simpleMessage(
      "This language will be the default for your clients in the app or website.",
    ),
    "defaultTheme": MessageLookupByLibrary.simpleMessage("Default Theme"),
    "defaultThemeDescription": MessageLookupByLibrary.simpleMessage(
      "This theme will be the default for your clients in the app or website.",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
    "deleteCategory": MessageLookupByLibrary.simpleMessage("Delete Category"),
    "deleteCategoryConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this category?",
    ),
    "deleteCategoryConfirmationMessageYouWillDeleteMainCategoryAlsoBecauseItsLastSubCategory":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this category?\nYou will delete main category also because it\'s last sub category",
        ),
    "deleteEmployee": MessageLookupByLibrary.simpleMessage("Delete Employee"),
    "deleteExpense": MessageLookupByLibrary.simpleMessage("Delete Expense"),
    "deleteOffer": MessageLookupByLibrary.simpleMessage("Delete Offer"),
    "deleteProductConfirmationBanner": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this Banner?",
    ),
    "deleteProductConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this product?",
    ),
    "deletePromoCode": MessageLookupByLibrary.simpleMessage(
      "Delete Promo Code",
    ),
    "deletePromoCodeConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this promo code?",
    ),
    "deleteSale": MessageLookupByLibrary.simpleMessage("Delete Sale"),
    "deleteTask": MessageLookupByLibrary.simpleMessage("Delete Task"),
    "deleted": MessageLookupByLibrary.simpleMessage("Deleted"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Deleted successfully",
    ),
    "delivered": MessageLookupByLibrary.simpleMessage("Delivered"),
    "delivery": MessageLookupByLibrary.simpleMessage("Delivering"),
    "deliveryAddress": MessageLookupByLibrary.simpleMessage("Delivery Address"),
    "deliveryAddressRemovedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Delivery Address removed successfully",
    ),
    "deliveryCostChangedTo": m2,
    "delivery_addresses": MessageLookupByLibrary.simpleMessage(
      "Delivery Addresses",
    ),
    "delivery_confirmation": MessageLookupByLibrary.simpleMessage(
      "Delivery Confirmation",
    ),
    "delivery_fee": MessageLookupByLibrary.simpleMessage("Shipping Cost"),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "discount": MessageLookupByLibrary.simpleMessage("Discount"),
    "discover__explorer": MessageLookupByLibrary.simpleMessage(
      "Discover & Explorer",
    ),
    "dismiss": MessageLookupByLibrary.simpleMessage("Dismiss"),
    "doNotHaveAnyOrder": MessageLookupByLibrary.simpleMessage(
      "You don\'t have any orders 🙂",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Done"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "dont_have_any_item_in_the_notification_list":
        MessageLookupByLibrary.simpleMessage(
          "D\'ont have any item in the notification list",
        ),
    "dont_have_any_item_in_your_cart": MessageLookupByLibrary.simpleMessage(
      "D\'ont have any item in your cart",
    ),
    "earnings": MessageLookupByLibrary.simpleMessage("Earnings"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editBanner": MessageLookupByLibrary.simpleMessage("Edit Banner"),
    "editBasicInfo": MessageLookupByLibrary.simpleMessage("Edit Basic Info"),
    "editCategory": MessageLookupByLibrary.simpleMessage("Edit category"),
    "editEmployee": MessageLookupByLibrary.simpleMessage("Edit Employee"),
    "editGlobalOfferError": MessageLookupByLibrary.simpleMessage(
      "The Percentage should be less than 100%",
    ),
    "editMainCategory": MessageLookupByLibrary.simpleMessage(
      "Edit Main Category",
    ),
    "editOrder": MessageLookupByLibrary.simpleMessage("Edit Order"),
    "editPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Edit Payment Method",
    ),
    "editPermissions": MessageLookupByLibrary.simpleMessage("Edit Permissions"),
    "editProduct": MessageLookupByLibrary.simpleMessage("Edit Product"),
    "editProfile": MessageLookupByLibrary.simpleMessage("Edit profile"),
    "editPromoCode": MessageLookupByLibrary.simpleMessage("Edit Promo Code"),
    "editShipping": MessageLookupByLibrary.simpleMessage("Edit Shipping"),
    "editSize": MessageLookupByLibrary.simpleMessage("Edit size"),
    "editTask": MessageLookupByLibrary.simpleMessage("Edit Task"),
    "editVendor": MessageLookupByLibrary.simpleMessage("Edit Vendor"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edited Successfully",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Email already exist",
    ),
    "emailAndWebsiteNameAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Email & Website Name already exist",
    ),
    "emailIsInvalid": MessageLookupByLibrary.simpleMessage("Email is invalid"),
    "email_address": MessageLookupByLibrary.simpleMessage("Email Address"),
    "email_field": MessageLookupByLibrary.simpleMessage("Email Field"),
    "email_to_reset_password": MessageLookupByLibrary.simpleMessage(
      "Email to reset password",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "englishDescription": MessageLookupByLibrary.simpleMessage(
      "English Description",
    ),
    "englishName": MessageLookupByLibrary.simpleMessage("English Name"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterColor": MessageLookupByLibrary.simpleMessage("Enter color"),
    "enterSize": MessageLookupByLibrary.simpleMessage("Enter size"),
    "enterValidEmail": MessageLookupByLibrary.simpleMessage(
      "Enter a valid email",
    ),
    "enterValidLink": MessageLookupByLibrary.simpleMessage(
      "Enter a valid link",
    ),
    "enterValidNumber": MessageLookupByLibrary.simpleMessage(
      "Enter a valid number",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Enter a valid phone number",
    ),
    "enterWebsiteName": MessageLookupByLibrary.simpleMessage(
      "Enter Website Name",
    ),
    "enterYourMessage": MessageLookupByLibrary.simpleMessage(
      "Enter your message !",
    ),
    "error_verify_email_settings": MessageLookupByLibrary.simpleMessage(
      "Error! Verify email settings",
    ),
    "exitTheApp": MessageLookupByLibrary.simpleMessage("Exit the app"),
    "expenses": MessageLookupByLibrary.simpleMessage("Expenses"),
    "expireDate": MessageLookupByLibrary.simpleMessage("Expire Date"),
    "expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "extraSettings": MessageLookupByLibrary.simpleMessage("Extra Settings"),
    "extras": MessageLookupByLibrary.simpleMessage("Extras"),
    "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
    "failed": MessageLookupByLibrary.simpleMessage("Failed"),
    "failedToSendNotificationsToUsers": MessageLookupByLibrary.simpleMessage(
      "Failed to send notifications to users",
    ),
    "faq": MessageLookupByLibrary.simpleMessage("Faq"),
    "faqsRefreshedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Faqs refreshed successfully",
    ),
    "favorite_products": MessageLookupByLibrary.simpleMessage(
      "Favorite Products",
    ),
    "favoritesRefreshedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Favorites refreshed successfully",
    ),
    "featured": MessageLookupByLibrary.simpleMessage("Featured"),
    "featuredProducts": MessageLookupByLibrary.simpleMessage(
      "Featured Products",
    ),
    "field_settings": MessageLookupByLibrary.simpleMessage("Field Settings"),
    "filterByDate": MessageLookupByLibrary.simpleMessage("Filter By Date"),
    "filterWithDateRange": MessageLookupByLibrary.simpleMessage(
      "Filter With Date Range",
    ),
    "floor": MessageLookupByLibrary.simpleMessage("Floor"),
    "free": MessageLookupByLibrary.simpleMessage("Free"),
    "freePlan": MessageLookupByLibrary.simpleMessage("Free Plan"),
    "freeShipping": MessageLookupByLibrary.simpleMessage("Free Shipping"),
    "freeWebsite": MessageLookupByLibrary.simpleMessage("Free Website"),
    "from": MessageLookupByLibrary.simpleMessage("From"),
    "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
    "full_address": MessageLookupByLibrary.simpleMessage("Full Address"),
    "full_name": MessageLookupByLibrary.simpleMessage("Full name"),
    "generalInformation": MessageLookupByLibrary.simpleMessage(
      "General Information",
    ),
    "generate": MessageLookupByLibrary.simpleMessage("Generate"),
    "globalDiscountError": MessageLookupByLibrary.simpleMessage(
      "Your global discount made some prices negative. Please adjust it.",
    ),
    "globalOffer": MessageLookupByLibrary.simpleMessage("Global Sale"),
    "globalOfferDec": MessageLookupByLibrary.simpleMessage(
      "It is a general Sale that is applied to all products in the store",
    ),
    "globalOfferDecRimender": m3,
    "goToHome": MessageLookupByLibrary.simpleMessage("Go To Home"),
    "guest_checkout": MessageLookupByLibrary.simpleMessage("Guest Checkout"),
    "help__support": MessageLookupByLibrary.simpleMessage("Help & Support"),
    "help_support": MessageLookupByLibrary.simpleMessage("Help & Support"),
    "help_supports": MessageLookupByLibrary.simpleMessage("Help & Supports"),
    "hide": MessageLookupByLibrary.simpleMessage("Hide"),
    "hint": MessageLookupByLibrary.simpleMessage("Hint"),
    "hint_full_address": MessageLookupByLibrary.simpleMessage(
      "12 Street, City 21663, Country",
    ),
    "history": MessageLookupByLibrary.simpleMessage("History"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "home_address": MessageLookupByLibrary.simpleMessage("Home Address"),
    "iHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "I have an account ? ",
    ),
    "i_dont_have_an_account": MessageLookupByLibrary.simpleMessage(
      "I don\'t have an account?",
    ),
    "i_forgot_password": MessageLookupByLibrary.simpleMessage(
      "I forgot password ?",
    ),
    "i_remember_my_password_return_to_login":
        MessageLookupByLibrary.simpleMessage(
          "I remember my password return to login",
        ),
    "ifYouDontNeedToManageProductStockMakeSureToTurnOnTheInventoryAndLeaveItEmpty":
        MessageLookupByLibrary.simpleMessage(
          "If you don\'t need to manage product stock make sure to turn on the inventory and leave it empty",
        ),
    "ifYouEnableThisCustomersWillSeeThePromoCodeInTheAppOrWebsitePromoList":
        MessageLookupByLibrary.simpleMessage(
          "If you enable this, customers will see the promo code in the app or website promo list",
        ),
    "ifYouNeedToChangeYourWebsiteNamePlease":
        MessageLookupByLibrary.simpleMessage(
          "If you need to change your website name please",
        ),
    "ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock":
        MessageLookupByLibrary.simpleMessage(
          "If you turn on the inventory you will remove size & colors stock",
        ),
    "ifYouTurnOofTheInventoryYouWillRemoveTheProductStock":
        MessageLookupByLibrary.simpleMessage(
          "If you turn off the inventory you will remove the product stock",
        ),
    "inStock": MessageLookupByLibrary.simpleMessage("In Stock"),
    "inactive": MessageLookupByLibrary.simpleMessage("Inactive"),
    "information": MessageLookupByLibrary.simpleMessage("Information"),
    "insertAnAdditionalInformationForThisOrder":
        MessageLookupByLibrary.simpleMessage(
          "Insert an additional information for this order",
        ),
    "instagram": MessageLookupByLibrary.simpleMessage("Instagram"),
    "invalidWebsiteName": MessageLookupByLibrary.simpleMessage(
      "Invalid Website Name",
    ),
    "inventory": MessageLookupByLibrary.simpleMessage("Inventory"),
    "inviteEmployeesToYourStore": MessageLookupByLibrary.simpleMessage(
      "Invite employees to your store and set\ntheir access permissions easily",
    ),
    "invoice": MessageLookupByLibrary.simpleMessage("Invoice"),
    "invoiceId": MessageLookupByLibrary.simpleMessage("Invoice Id"),
    "invoices": MessageLookupByLibrary.simpleMessage("Invoices"),
    "isActive": MessageLookupByLibrary.simpleMessage("Is Active"),
    "isDone": MessageLookupByLibrary.simpleMessage("Is Done"),
    "isPaid": MessageLookupByLibrary.simpleMessage("Is Paid"),
    "isPercentage": MessageLookupByLibrary.simpleMessage("Is Percentage"),
    "items": MessageLookupByLibrary.simpleMessage("Items"),
    "itsNotActiveAndWillNotBeDisplayedToYourCustomers":
        MessageLookupByLibrary.simpleMessage(
          "It\'s not active and will not be displayed to your customers",
        ),
    "john_doe": MessageLookupByLibrary.simpleMessage("John Doe"),
    "keep_your_old_meals_of_this_market": MessageLookupByLibrary.simpleMessage(
      "Keep your old meals of this market",
    ),
    "languages": MessageLookupByLibrary.simpleMessage("Languages"),
    "large": MessageLookupByLibrary.simpleMessage("Large"),
    "lastMonth": MessageLookupByLibrary.simpleMessage("Last Month"),
    "lastQuarter": MessageLookupByLibrary.simpleMessage("Last Quarter"),
    "lastSemiAnnual": MessageLookupByLibrary.simpleMessage("Last Semi Annual"),
    "lastWeek": MessageLookupByLibrary.simpleMessage("Last Week"),
    "lastYear": MessageLookupByLibrary.simpleMessage("Last Year"),
    "latestOrders": MessageLookupByLibrary.simpleMessage("Latest Orders"),
    "left": MessageLookupByLibrary.simpleMessage("left"),
    "letsLoginToYourAccountFirst": MessageLookupByLibrary.simpleMessage(
      "Let\'s Login to your\naccount first!",
    ),
    "lets_start_with_login": MessageLookupByLibrary.simpleMessage(
      "Let\'s Start with Login!",
    ),
    "lets_start_with_register": MessageLookupByLibrary.simpleMessage(
      "Let\'s Start with register!",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Light"),
    "light_mode": MessageLookupByLibrary.simpleMessage("Light Mode"),
    "link": MessageLookupByLibrary.simpleMessage("Link"),
    "log_out": MessageLookupByLibrary.simpleMessage("Log out"),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "logoSize": MessageLookupByLibrary.simpleMessage("Logo Size"),
    "long_press_to_edit_item_swipe_item_to_delete_it":
        MessageLookupByLibrary.simpleMessage(
          "Long press to edit item, swipe item to delete it",
        ),
    "mainCategories": MessageLookupByLibrary.simpleMessage("Main Categories"),
    "mainCategoriesTab": MessageLookupByLibrary.simpleMessage(
      "Main Categories",
    ),
    "mainCategory": MessageLookupByLibrary.simpleMessage("Main Category"),
    "mainCategorySettingsDesc": MessageLookupByLibrary.simpleMessage(
      "Create a main category to group related subcategories under one parent. For example: a \'Men\' category can include \'Jackets\', \'T-Shirts\', and \'Pants\'.",
    ),
    "manageCategoriesProducts": MessageLookupByLibrary.simpleMessage(
      "Manage Categories & Products",
    ),
    "manageEmployees": MessageLookupByLibrary.simpleMessage("Manage Employees"),
    "manageOrders": MessageLookupByLibrary.simpleMessage("Manage Orders"),
    "manageSizesAndColors": MessageLookupByLibrary.simpleMessage(
      "Manage Sizes & Colors",
    ),
    "manageStoreOrders": MessageLookupByLibrary.simpleMessage(
      "Manage Store Orders",
    ),
    "market_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Market refreshed successfully",
    ),
    "maxUploadFileSizeIsOnly10MB": MessageLookupByLibrary.simpleMessage(
      "Max upload image size is only 10 MB",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "Max upload images is only 4",
    ),
    "medium": MessageLookupByLibrary.simpleMessage("Medium"),
    "messages": MessageLookupByLibrary.simpleMessage("Messages"),
    "minimumOrderCost": MessageLookupByLibrary.simpleMessage(
      "Minimum Order Cost",
    ),
    "monthly": MessageLookupByLibrary.simpleMessage("Monthly"),
    "more": MessageLookupByLibrary.simpleMessage("more"),
    "myMarkets": MessageLookupByLibrary.simpleMessage("My Markets"),
    "myWebsite": MessageLookupByLibrary.simpleMessage("My Website"),
    "myWebsiteLink": MessageLookupByLibrary.simpleMessage("My Website Link"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "name_field": MessageLookupByLibrary.simpleMessage("Name Field"),
    "newMessageFrom": MessageLookupByLibrary.simpleMessage("New message from"),
    "newOrder": MessageLookupByLibrary.simpleMessage("new order"),
    "newOrdersAlertBell": MessageLookupByLibrary.simpleMessage(
      "New Orders Alert Bell",
    ),
    "new_address_added_successfully": MessageLookupByLibrary.simpleMessage(
      "New Address added successfully",
    ),
    "new_order_from_costumer": MessageLookupByLibrary.simpleMessage(
      "New Order from costumer",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "noAccessTo": MessageLookupByLibrary.simpleMessage("No Access to"),
    "noAreasFound": MessageLookupByLibrary.simpleMessage("No Areas Found"),
    "noBanners": MessageLookupByLibrary.simpleMessage("No Banners"),
    "noCategories": MessageLookupByLibrary.simpleMessage("No categories"),
    "noChangesToSave": MessageLookupByLibrary.simpleMessage(
      "No changes to save",
    ),
    "noColorsAdded": MessageLookupByLibrary.simpleMessage("No Colors Added"),
    "noData": MessageLookupByLibrary.simpleMessage("No data"),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage(
      "No Data Available",
    ),
    "noDataFound": MessageLookupByLibrary.simpleMessage("No Data Found"),
    "noDomainFees": MessageLookupByLibrary.simpleMessage("(No domain fees)"),
    "noEmail": MessageLookupByLibrary.simpleMessage("No Email"),
    "noExpenses": MessageLookupByLibrary.simpleMessage("No Expenses"),
    "noInternet": MessageLookupByLibrary.simpleMessage(
      "No internet connection!",
    ),
    "noName": MessageLookupByLibrary.simpleMessage("No Name"),
    "noNotificationsFound": MessageLookupByLibrary.simpleMessage(
      "No Notifications Found",
    ),
    "noOrdersFound": MessageLookupByLibrary.simpleMessage("No Orders Found"),
    "noPaymentMethods": MessageLookupByLibrary.simpleMessage(
      "No Payment Methods",
    ),
    "noProductsFound": MessageLookupByLibrary.simpleMessage(
      "No products found",
    ),
    "noProductsInThisCategory": MessageLookupByLibrary.simpleMessage(
      "No products in this category",
    ),
    "noPromoCodes": MessageLookupByLibrary.simpleMessage("No Promo Codes"),
    "noResultFound": MessageLookupByLibrary.simpleMessage("No result found"),
    "noSelectedSubCategories": MessageLookupByLibrary.simpleMessage(
      "No Sub Categories",
    ),
    "noSizesAdded": MessageLookupByLibrary.simpleMessage("No Sizes Added"),
    "noStoreInvoices": MessageLookupByLibrary.simpleMessage(
      "No Store Invoices",
    ),
    "noSubCategories": MessageLookupByLibrary.simpleMessage(
      "No sub-categories available",
    ),
    "noSubscriptionRequests": MessageLookupByLibrary.simpleMessage(
      "No Subscription Requests",
    ),
    "noTasks": MessageLookupByLibrary.simpleMessage("No Tasks"),
    "noUsersFound": MessageLookupByLibrary.simpleMessage("No Users Found"),
    "noUsersOrders": MessageLookupByLibrary.simpleMessage("No Users Orders"),
    "noVendorsFound": MessageLookupByLibrary.simpleMessage("No Vendors Found"),
    "not_a_valid_address": MessageLookupByLibrary.simpleMessage(
      "Not a valid address",
    ),
    "not_a_valid_biography": MessageLookupByLibrary.simpleMessage(
      "Not a valid biography",
    ),
    "not_a_valid_cvc": MessageLookupByLibrary.simpleMessage("Not a valid CVC"),
    "not_a_valid_date": MessageLookupByLibrary.simpleMessage(
      "Not a valid date",
    ),
    "not_a_valid_email": MessageLookupByLibrary.simpleMessage(
      "Not a valid email",
    ),
    "not_a_valid_full_name": MessageLookupByLibrary.simpleMessage(
      "Not a valid full name",
    ),
    "not_a_valid_number": MessageLookupByLibrary.simpleMessage(
      "Not a valid number",
    ),
    "not_a_valid_phone": MessageLookupByLibrary.simpleMessage(
      "Not a valid phone",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "notificationWasRemoved": MessageLookupByLibrary.simpleMessage(
      "Notification was removed",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "notifications_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Notifications refreshed successfully",
    ),
    "off": MessageLookupByLibrary.simpleMessage("Off"),
    "on": MessageLookupByLibrary.simpleMessage("On"),
    "onSale": MessageLookupByLibrary.simpleMessage("On Sale"),
    "oneMonth": MessageLookupByLibrary.simpleMessage("One Month"),
    "oneTimePrice": MessageLookupByLibrary.simpleMessage("One Time Price"),
    "one_page_checkout": MessageLookupByLibrary.simpleMessage(
      "One Page Checkout",
    ),
    "open": MessageLookupByLibrary.simpleMessage("Open"),
    "optional": MessageLookupByLibrary.simpleMessage("Optional"),
    "orderDetails": MessageLookupByLibrary.simpleMessage("Order Details"),
    "orderId": MessageLookupByLibrary.simpleMessage("Order Id"),
    "orderIdHasBeenCanceled": m5,
    "orderReceived": MessageLookupByLibrary.simpleMessage("Order Received"),
    "orderStatus": MessageLookupByLibrary.simpleMessage("Order Status"),
    "orderStatusChangedTo": m4,
    "orderTotal": MessageLookupByLibrary.simpleMessage("Order Total"),
    "order_details": MessageLookupByLibrary.simpleMessage("Order Details"),
    "order_id": MessageLookupByLibrary.simpleMessage("Order Id"),
    "order_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Order refreshed successfully",
    ),
    "order_satatus_changed": MessageLookupByLibrary.simpleMessage(
      "Order status changed",
    ),
    "ordered_products": MessageLookupByLibrary.simpleMessage(
      "Ordered Products",
    ),
    "orders": MessageLookupByLibrary.simpleMessage("Orders"),
    "ordersAndShipping": MessageLookupByLibrary.simpleMessage(
      "Orders & Shipping",
    ),
    "ordersSettings": MessageLookupByLibrary.simpleMessage("Orders Settings"),
    "orders_history": MessageLookupByLibrary.simpleMessage("Orders History"),
    "orders_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Orders refreshed successfully",
    ),
    "outOfStock": MessageLookupByLibrary.simpleMessage("Out Of Stock"),
    "paid": MessageLookupByLibrary.simpleMessage("Paid"),
    "paidAmount": MessageLookupByLibrary.simpleMessage("Paid Amount"),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordLength": MessageLookupByLibrary.simpleMessage(
      "Password length should be more than 8 characters",
    ),
    "payNow": MessageLookupByLibrary.simpleMessage("Pay Now"),
    "payWithInstapay": MessageLookupByLibrary.simpleMessage(
      "Pay with Instapay",
    ),
    "payWithVodafoneCash": MessageLookupByLibrary.simpleMessage(
      "Pay with Vodafone Cash",
    ),
    "paymentAttachment": MessageLookupByLibrary.simpleMessage(
      "Payment Attachment",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("Payment Methods"),
    "paymentSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Payment sent successfully",
    ),
    "payment_mode": MessageLookupByLibrary.simpleMessage("Payment Mode"),
    "payment_settings": MessageLookupByLibrary.simpleMessage(
      "Payment Settings",
    ),
    "payment_settings_updated_successfully":
        MessageLookupByLibrary.simpleMessage(
          "Payment settings updated successfully",
        ),
    "paymentsAndPromotions": MessageLookupByLibrary.simpleMessage(
      "Payments & Promotions",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("Pending"),
    "permissions": MessageLookupByLibrary.simpleMessage("Permissions"),
    "phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "phone2": MessageLookupByLibrary.simpleMessage("Phone 2"),
    "phoneIsInvalid": MessageLookupByLibrary.simpleMessage("Phone is invalid"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
    "phoneOrPayLink": MessageLookupByLibrary.simpleMessage(
      "phone number or pay link",
    ),
    "phone_field": MessageLookupByLibrary.simpleMessage("Phone Field"),
    "pickAColor": MessageLookupByLibrary.simpleMessage("Pick a color !"),
    "pickup": MessageLookupByLibrary.simpleMessage("Pickup"),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "playStore": MessageLookupByLibrary.simpleMessage("Play Store"),
    "pleaseAddCategoryFirst": MessageLookupByLibrary.simpleMessage(
      "Please add category first",
    ),
    "pleaseAddPriceToYourAllSizes": MessageLookupByLibrary.simpleMessage(
      "Please add price to your all sizes",
    ),
    "pleaseAddSizes": MessageLookupByLibrary.simpleMessage("Please Add Sizes"),
    "pleaseAttachPaymentAttachment": MessageLookupByLibrary.simpleMessage(
      "Please attach payment attachment",
    ),
    "pleaseChooseQrType": MessageLookupByLibrary.simpleMessage(
      "Please choose QR Type",
    ),
    "pleaseContactSupport": MessageLookupByLibrary.simpleMessage(
      "Please contact support",
    ),
    "pleasePickImage": MessageLookupByLibrary.simpleMessage(
      "Please pick an image",
    ),
    "pleaseSelect": MessageLookupByLibrary.simpleMessage("Please select"),
    "pleaseSelectAtLeastOneRole": MessageLookupByLibrary.simpleMessage(
      "Please select at least one role",
    ),
    "pleaseSelectSubCategories": MessageLookupByLibrary.simpleMessage(
      "Please select Sub Categories",
    ),
    "premiumWebsite": MessageLookupByLibrary.simpleMessage("Premium Website"),
    "preparing": MessageLookupByLibrary.simpleMessage("Preparing"),
    "price": MessageLookupByLibrary.simpleMessage("Price"),
    "pricingPlan": MessageLookupByLibrary.simpleMessage("Pricing Plan"),
    "primaryColor": MessageLookupByLibrary.simpleMessage("Primary Color"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "product": MessageLookupByLibrary.simpleMessage("Product"),
    "productLimitReached": MessageLookupByLibrary.simpleMessage(
      "Products limit reached !\nPlease upgrade your plan...",
    ),
    "productRefreshedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Product refreshed successfully",
    ),
    "productStatus": MessageLookupByLibrary.simpleMessage("Product Status"),
    "productStock": MessageLookupByLibrary.simpleMessage("Product Stock"),
    "products": MessageLookupByLibrary.simpleMessage("Products"),
    "productsAndCategories": MessageLookupByLibrary.simpleMessage(
      "Products & Categories",
    ),
    "products_ordered": MessageLookupByLibrary.simpleMessage(
      "Products Ordered",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profile_settings": MessageLookupByLibrary.simpleMessage(
      "Profile Settings",
    ),
    "profile_settings_updated_successfully":
        MessageLookupByLibrary.simpleMessage(
          "Profile settings updated successfully",
        ),
    "promoCode": MessageLookupByLibrary.simpleMessage("Promo Code"),
    "promoCodeAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Promo code already exist",
    ),
    "promoCodes": MessageLookupByLibrary.simpleMessage("Promo Codes"),
    "qr": MessageLookupByLibrary.simpleMessage("QR"),
    "qrCircle": MessageLookupByLibrary.simpleMessage("QR Circle"),
    "qrLanding": MessageLookupByLibrary.simpleMessage("QR Landing"),
    "qrSettings": MessageLookupByLibrary.simpleMessage("QR Settings"),
    "qrSquare": MessageLookupByLibrary.simpleMessage("QR Square"),
    "qrType": MessageLookupByLibrary.simpleMessage("QR Type"),
    "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
    "ready": MessageLookupByLibrary.simpleMessage("Ready"),
    "recent_orders": MessageLookupByLibrary.simpleMessage("Recent Orders"),
    "recents_search": MessageLookupByLibrary.simpleMessage("Recents Search"),
    "refunded": MessageLookupByLibrary.simpleMessage("Refunded"),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "remainingAmount": MessageLookupByLibrary.simpleMessage("Remaining Amount"),
    "renewNow": MessageLookupByLibrary.simpleMessage("Renew Now"),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "reportsAndOperations": MessageLookupByLibrary.simpleMessage(
      "Reports & Operations",
    ),
    "requestApproved": MessageLookupByLibrary.simpleMessage("Request Approved"),
    "requestDate": MessageLookupByLibrary.simpleMessage("Request Date"),
    "requiredField": MessageLookupByLibrary.simpleMessage("Required field"),
    "required_field": MessageLookupByLibrary.simpleMessage("Required Field"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "reset_cart": MessageLookupByLibrary.simpleMessage("Reset Cart?"),
    "reset_your_cart_and_order_meals_form_this_market":
        MessageLookupByLibrary.simpleMessage(
          "Reset your cart and order meals form this market",
        ),
    "roles": MessageLookupByLibrary.simpleMessage("roles"),
    "sale": MessageLookupByLibrary.simpleMessage("Sale"),
    "salePrice": MessageLookupByLibrary.simpleMessage("Sale Price"),
    "salePriceMustBeLessThanPrice": MessageLookupByLibrary.simpleMessage(
      "Sale Price must be less than Price",
    ),
    "salePriceShouldBeLessThanPrice": MessageLookupByLibrary.simpleMessage(
      "Sale price should be less than regular price",
    ),
    "saleQuantityPrice": MessageLookupByLibrary.simpleMessage(
      "Sale Quantity Price",
    ),
    "saleQuantityPriceDesc": MessageLookupByLibrary.simpleMessage(
      "Set how many items the customer must buy to get the sale price",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchBy": MessageLookupByLibrary.simpleMessage("Search By"),
    "searchByNameOrEmail": MessageLookupByLibrary.simpleMessage(
      "Search by name or email",
    ),
    "searchEmployees": MessageLookupByLibrary.simpleMessage("Search Employees"),
    "searchProduct": MessageLookupByLibrary.simpleMessage("Search Product"),
    "select": MessageLookupByLibrary.simpleMessage("Select"),
    "selectAll": MessageLookupByLibrary.simpleMessage("Select All"),
    "selectAreas": MessageLookupByLibrary.simpleMessage("Select Areas"),
    "selectCategories": MessageLookupByLibrary.simpleMessage(
      "Select categories",
    ),
    "selectCountry": MessageLookupByLibrary.simpleMessage("Select Country"),
    "selectCurrency": MessageLookupByLibrary.simpleMessage("Select Currency"),
    "selectProducts": MessageLookupByLibrary.simpleMessage("Select Products"),
    "selectVendor": MessageLookupByLibrary.simpleMessage("Select Vendor"),
    "selectYourBusinessType": MessageLookupByLibrary.simpleMessage(
      "Select your business type",
    ),
    "selectYourCountryForTheAppsYouCanChangedLatter":
        MessageLookupByLibrary.simpleMessage(
          "Select Your Country For The Apps\nYou Can Change It Latter",
        ),
    "selectYourCurrencyForTheAppsYouCanChangedLatter":
        MessageLookupByLibrary.simpleMessage(
          "Select Your Currency For The Apps\nYou Can Change It Latter",
        ),
    "select_your_preferred_languages": MessageLookupByLibrary.simpleMessage(
      "Select your preferred languages",
    ),
    "selectedAreas": MessageLookupByLibrary.simpleMessage("Selected Areas"),
    "selectedProducts": MessageLookupByLibrary.simpleMessage(
      "Selected Products",
    ),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendNotification": MessageLookupByLibrary.simpleMessage(
      "Send Notification",
    ),
    "sendRequest": MessageLookupByLibrary.simpleMessage("Send Request"),
    "sendSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Sent Successfully",
    ),
    "send_password_reset_link": MessageLookupByLibrary.simpleMessage(
      "Send link",
    ),
    "senderInfo": MessageLookupByLibrary.simpleMessage("Sender Information"),
    "setting": MessageLookupByLibrary.simpleMessage("Settings"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "share": MessageLookupByLibrary.simpleMessage("Share"),
    "shareLink": MessageLookupByLibrary.simpleMessage("Share Link"),
    "shareQr": MessageLookupByLibrary.simpleMessage("Share QR"),
    "shippingCost": MessageLookupByLibrary.simpleMessage("Shipping Cost"),
    "shopping_cart": MessageLookupByLibrary.simpleMessage("Shopping Cart"),
    "should_be_a_valid_email": MessageLookupByLibrary.simpleMessage(
      "Should be a valid email",
    ),
    "should_be_more_than_3_characters": MessageLookupByLibrary.simpleMessage(
      "Should be more than 3 characters",
    ),
    "should_be_more_than_3_letters": MessageLookupByLibrary.simpleMessage(
      "Should be more than 3 letters",
    ),
    "should_be_more_than_6_letters": MessageLookupByLibrary.simpleMessage(
      "Should be more than 6 letters",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage("Show Details"),
    "showInList": MessageLookupByLibrary.simpleMessage("Show In List"),
    "showLogo": MessageLookupByLibrary.simpleMessage("Show My Logo"),
    "showPricing": MessageLookupByLibrary.simpleMessage("Show Pricing"),
    "show_checkout": MessageLookupByLibrary.simpleMessage("Show Checkout"),
    "show_field": MessageLookupByLibrary.simpleMessage("Show Field"),
    "sixMonths": MessageLookupByLibrary.simpleMessage("6 Months"),
    "size": MessageLookupByLibrary.simpleMessage("Size"),
    "sizeAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Size already exist",
    ),
    "sizePricing": MessageLookupByLibrary.simpleMessage("Size Pricing"),
    "sizeStockIInventoryIsOff": MessageLookupByLibrary.simpleMessage(
      "Size Stock is off",
    ),
    "sizes": MessageLookupByLibrary.simpleMessage("Sizes"),
    "sizesAndColors": MessageLookupByLibrary.simpleMessage("Sizes & Colors"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "small": MessageLookupByLibrary.simpleMessage("Small"),
    "socialLinks": MessageLookupByLibrary.simpleMessage("Social Links & About"),
    "socialLinksHint": MessageLookupByLibrary.simpleMessage(
      "If you fill in any of these fields, they will be visible to your clients. If you leave any of them empty, those specific links will not be shown.",
    ),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage(
      "Something went wrong",
    ),
    "sortCategories": MessageLookupByLibrary.simpleMessage("Sort Categories"),
    "sortCategoriesDescription": MessageLookupByLibrary.simpleMessage(
      "Hold and drag a category to change its position. Arrange them the way you prefer.",
    ),
    "sortMainCategories": MessageLookupByLibrary.simpleMessage(
      "Sort Main Categories",
    ),
    "sortMainCategoriesDescription": MessageLookupByLibrary.simpleMessage(
      "Drag and drop main categories to change their order. Arrange them the way you prefer.",
    ),
    "sortProducts": MessageLookupByLibrary.simpleMessage("Sort Products"),
    "sortProductsDescription": MessageLookupByLibrary.simpleMessage(
      "Drag and drop products to change their order. Arrange them the way you prefer.",
    ),
    "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
    "start_exploring": MessageLookupByLibrary.simpleMessage("Start Exploring"),
    "state": MessageLookupByLibrary.simpleMessage("state"),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "stepsToCreateYourFreeWebSite": MessageLookupByLibrary.simpleMessage(
      "Steps to create your free website",
    ),
    "stepsToCreateYourWebSite": MessageLookupByLibrary.simpleMessage(
      "Steps to create your website",
    ),
    "steps_checkout": MessageLookupByLibrary.simpleMessage("Steps Checkout"),
    "stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "store": MessageLookupByLibrary.simpleMessage("Store"),
    "storeActive": MessageLookupByLibrary.simpleMessage("Store Active"),
    "storeAppearance": MessageLookupByLibrary.simpleMessage("Store Appearance"),
    "storeInvoices": MessageLookupByLibrary.simpleMessage("Store Invoices"),
    "streetName": MessageLookupByLibrary.simpleMessage("Street Name"),
    "subCategories": MessageLookupByLibrary.simpleMessage("Sub Categories"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "subscribe": MessageLookupByLibrary.simpleMessage("Subscribe"),
    "subscribeNow": MessageLookupByLibrary.simpleMessage("Subscribe Now"),
    "subscriptionPlan": MessageLookupByLibrary.simpleMessage(
      "Subscription Plan",
    ),
    "subscriptionRequestApprovedSuccessfully":
        MessageLookupByLibrary.simpleMessage(
          "Subscription request approved successfully",
        ),
    "subscriptionRequests": MessageLookupByLibrary.simpleMessage(
      "Subscription Requests",
    ),
    "subscriptionType": MessageLookupByLibrary.simpleMessage(
      "Subscription Type",
    ),
    "subtotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
    "suggestions": MessageLookupByLibrary.simpleMessage("Suggestions"),
    "suggestionsAreas": MessageLookupByLibrary.simpleMessage(
      "Suggestions Areas",
    ),
    "swip_left_the_notification_to_delete_or_read__unread":
        MessageLookupByLibrary.simpleMessage(
          "Swipe left the notification to delete or read / unread it",
        ),
    "system": MessageLookupByLibrary.simpleMessage("System"),
    "tapBackAgainToLeave": MessageLookupByLibrary.simpleMessage(
      "Tap back again to leave",
    ),
    "taskDone": MessageLookupByLibrary.simpleMessage("Done"),
    "tasks": MessageLookupByLibrary.simpleMessage("Tasks"),
    "tax": MessageLookupByLibrary.simpleMessage("TAX"),
    "template": MessageLookupByLibrary.simpleMessage("Template"),
    "termsAndCondition": MessageLookupByLibrary.simpleMessage(
      "Terms & condition",
    ),
    "theConversationWithIsDismissed": m6,
    "theTemplate": MessageLookupByLibrary.simpleMessage("Template"),
    "the_address_updated_successfully": MessageLookupByLibrary.simpleMessage(
      "The address updated successfully",
    ),
    "the_product_was_removed_from_your_cart":
        MessageLookupByLibrary.simpleMessage(
          "The \$productName was removed from your cart",
        ),
    "themeAndLanguageSettings": MessageLookupByLibrary.simpleMessage(
      "Theme & Language Settings",
    ),
    "thisAccountNotExist": MessageLookupByLibrary.simpleMessage(
      "This account not exist",
    ),
    "thisNotificationHasMarkedAsRead": MessageLookupByLibrary.simpleMessage(
      "This notification has marked as read",
    ),
    "thisNotificationHasMarkedAsUnRead": MessageLookupByLibrary.simpleMessage(
      "This notification has marked as un read",
    ),
    "thisOrderUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "This order updated successfully",
    ),
    "thisProductIsDeleted": MessageLookupByLibrary.simpleMessage(
      "This product is deleted",
    ),
    "thisProductWasAddedToFavorite": MessageLookupByLibrary.simpleMessage(
      "This product was added to favorite",
    ),
    "thisProductWasRemovedFromFavorites": MessageLookupByLibrary.simpleMessage(
      "This product was removed from favorites",
    ),
    "threeMonths": MessageLookupByLibrary.simpleMessage("3 Months"),
    "tiktok": MessageLookupByLibrary.simpleMessage("Tiktok"),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "to": MessageLookupByLibrary.simpleMessage("To"),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalEarning": MessageLookupByLibrary.simpleMessage("Total Earnings"),
    "totalMarkets": MessageLookupByLibrary.simpleMessage("Total Markets"),
    "totalOrders": MessageLookupByLibrary.simpleMessage("Total Orders"),
    "totalOrdersHome": MessageLookupByLibrary.simpleMessage("Total\nOrders"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price:"),
    "totalProducts": MessageLookupByLibrary.simpleMessage("Total Products"),
    "totalProfit": MessageLookupByLibrary.simpleMessage("Total\nEarnings"),
    "totalTasks": MessageLookupByLibrary.simpleMessage("Total Tasks"),
    "tracking_order": MessageLookupByLibrary.simpleMessage("Tracking Order"),
    "tracking_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "Tracking refreshed successfully",
    ),
    "typeToStartChat": MessageLookupByLibrary.simpleMessage(
      "Type to start chat",
    ),
    "uncompleted": MessageLookupByLibrary.simpleMessage("Uncompleted"),
    "unknown": MessageLookupByLibrary.simpleMessage("Unknown"),
    "unlimited": MessageLookupByLibrary.simpleMessage("Unlimited"),
    "unpaid": MessageLookupByLibrary.simpleMessage("UnPaid"),
    "updateAvailable": MessageLookupByLibrary.simpleMessage("Update Available"),
    "updateAvailableDesc": MessageLookupByLibrary.simpleMessage(
      "A new version of the app is available. Please update the app to continue using it.",
    ),
    "updateNow": MessageLookupByLibrary.simpleMessage("Update Now"),
    "updateSelected": MessageLookupByLibrary.simpleMessage("Update Selected"),
    "updatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Updated successfully",
    ),
    "upgradePlan": MessageLookupByLibrary.simpleMessage("Upgrade Plan"),
    "userName": MessageLookupByLibrary.simpleMessage("User Name"),
    "userPhone": MessageLookupByLibrary.simpleMessage("User Phone"),
    "users": MessageLookupByLibrary.simpleMessage("Users"),
    "usersOrders": MessageLookupByLibrary.simpleMessage("Users Orders"),
    "vendorDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Vendor deleted successfully",
    ),
    "vendorStatus": MessageLookupByLibrary.simpleMessage("Vendor Status"),
    "vendors": MessageLookupByLibrary.simpleMessage("Vendors"),
    "verify": MessageLookupByLibrary.simpleMessage("Verify"),
    "verify_your_internet_connection": MessageLookupByLibrary.simpleMessage(
      "Verify your internet connection",
    ),
    "verify_your_quantity_and_click_checkout":
        MessageLookupByLibrary.simpleMessage(
          "Verify your quantity and click checkout",
        ),
    "version": MessageLookupByLibrary.simpleMessage("Version"),
    "view": MessageLookupByLibrary.simpleMessage("View"),
    "viewAttachment": MessageLookupByLibrary.simpleMessage(
      "View Payment Attachment",
    ),
    "viewCategoriesProducts": MessageLookupByLibrary.simpleMessage(
      "View Categories & Products",
    ),
    "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
    "viewOrders": MessageLookupByLibrary.simpleMessage("View Orders"),
    "visitWebsite": MessageLookupByLibrary.simpleMessage("Visit Website"),
    "waitingPayment": MessageLookupByLibrary.simpleMessage(
      "Waiting Payment...",
    ),
    "webLinkMessage": MessageLookupByLibrary.simpleMessage(
      "Welcome To IDEA2APP - You can see your website from this link below.\nWe refer to adding your products first to see your website in the best way.",
    ),
    "website": MessageLookupByLibrary.simpleMessage("Website"),
    "websiteName": MessageLookupByLibrary.simpleMessage("Website Name"),
    "websiteNameAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "Website Name already exist",
    ),
    "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
    "whatTheySay": MessageLookupByLibrary.simpleMessage("What They Say ?"),
    "whatsApp": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "would_you_please_confirm_if_you_have_delivered_all_meals":
        MessageLookupByLibrary.simpleMessage(
          "Would you please confirm if you have delivered all meals to client",
        ),
    "wrong_email_or_password": MessageLookupByLibrary.simpleMessage(
      "Wrong email or password",
    ),
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice":
        MessageLookupByLibrary.simpleMessage(
          "You can\'t change the price of the product because it is set by the size price !",
        ),
    "youDontHaveAnyConversations": MessageLookupByLibrary.simpleMessage(
      "You don\'t have any conversations",
    ),
    "youDontHaveMarketsPleaseSigninUsingAdminPanelAnd":
        MessageLookupByLibrary.simpleMessage(
          "You don\'t have markets, please sign-in using admin panel and open new market",
        ),
    "youHaveNewOrder": MessageLookupByLibrary.simpleMessage(
      "You have new order",
    ),
    "youHaveNewOrderPleaseCheckYourLatestOrders":
        MessageLookupByLibrary.simpleMessage(
          "You have new order, please check your latest orders !",
        ),
    "youMayNeedToReInstallTheAppToApplyChanges":
        MessageLookupByLibrary.simpleMessage(
          "You may need to re-install the app to apply changes",
        ),
    "youMust": MessageLookupByLibrary.simpleMessage("You must "),
    "youMustAddAtLeastOneName": MessageLookupByLibrary.simpleMessage(
      "You must add at least one name in Arabic or English.",
    ),
    "youMustAddMinQuantitySaleNumberAndQuantitySale":
        MessageLookupByLibrary.simpleMessage(
          "You must add min quantity sale number and quantity sale",
        ),
    "youMustAddOneOfThoseDescriptions": MessageLookupByLibrary.simpleMessage(
      "You must add at least one description in Arabic or English.",
    ),
    "youMustAddOneOfThoseNames": MessageLookupByLibrary.simpleMessage(
      "You must add one of those names",
    ),
    "youMustAddOneOfThoseTitles": MessageLookupByLibrary.simpleMessage(
      "You must add at least one title in Arabic or English.",
    ),
    "youMustAddStockForColorOrSizes": MessageLookupByLibrary.simpleMessage(
      "you must add stock for the\ncolors or sizes",
    ),
    "youMustButCostForAllAreasYouSelected":
        MessageLookupByLibrary.simpleMessage(
          "You must add cost for all areas you selected",
        ),
    "youMustTurnOffInventoryFirst": MessageLookupByLibrary.simpleMessage(
      "You must turn off inventory first",
    ),
    "youMustTurnOnInventoryFirst": MessageLookupByLibrary.simpleMessage(
      "You must turn on inventory first",
    ),
    "you_dont_have_any_order_assigned_to_you":
        MessageLookupByLibrary.simpleMessage(
          "You don\'t have any order assigned to you!",
        ),
    "you_must_add_products_of_the_same_markets_choose_one":
        MessageLookupByLibrary.simpleMessage(
          "You must add products of the same markets choose one markets only!",
        ),
    "yourAccountIsNotActive": MessageLookupByLibrary.simpleMessage(
      "Your account is not active",
    ),
    "yourAccountIsNotActivePleaseRenewYourSubscription":
        MessageLookupByLibrary.simpleMessage(
          "Your account is not active, please renew your subscription !",
        ),
    "yourDeliveryCostHasBeenChanged": MessageLookupByLibrary.simpleMessage(
      "Your delivery cost has been changed",
    ),
    "yourOrderStatusHasBeenChanged": MessageLookupByLibrary.simpleMessage(
      "Your order status has been changed",
    ),
    "yourRequestSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Your request has been successfully sent and is currently under review. We will provide a response as soon as possible.",
    ),
    "yourSettings": MessageLookupByLibrary.simpleMessage("Your Settings"),
    "yourSubscriptionHasExpired": MessageLookupByLibrary.simpleMessage(
      "Your subscription has expired",
    ),
    "yourWebsiteName": MessageLookupByLibrary.simpleMessage(
      "your-website-name",
    ),
    "your_address": MessageLookupByLibrary.simpleMessage("Your Address"),
    "your_biography": MessageLookupByLibrary.simpleMessage("Your biography"),
    "your_have_an_order_assigned_to_you": MessageLookupByLibrary.simpleMessage(
      "Your have an order assigned to you",
    ),
    "your_reset_link_has_been_sent_to_your_email":
        MessageLookupByLibrary.simpleMessage(
          "Your reset link has been sent to your email",
        ),
    "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
  };
}
