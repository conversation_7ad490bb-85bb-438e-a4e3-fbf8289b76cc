{"login": "<PERSON><PERSON>", "skip": "<PERSON><PERSON>", "about": "About", "submit": "Submit", "verify": "Verify", "select_your_preferred_languages": "Select your preferred languages", "order_id": "Order Id", "category": "Category", "checkout": "Checkout", "payment_mode": "Payment Mode", "aboutDescription": "As a tech company, we specialize in software solutions, offering a diverse range of products and expertise in developing bespoke applications. Our team is dedicated to crafting innovative software products, and we have the capability to create tailored software apps designed to meet your specific requirements. In addition, we offer a cutting-edge vendor app that empowers you to efficiently manage your business. We are committed to enhancing your business through our advanced technology solutions.", "subtotal": "Subtotal", "total": "Total", "totalPrice": "Total Price:", "favorite_products": "Favorite Products", "extras": "Extras", "faq": "Faq", "help_supports": "Help & Supports", "app_language": "App Language", "i_forgot_password": "I forgot password ?", "i_dont_have_an_account": "I don't have an account?", "notifications": "Notifications", "whatsApp": "WhatsApp", "youtube": "YouTube", "tax": "TAX", "editProfile": "Edit profile", "profile": "Profile", "home": "Home", "cash_on_delivery": "Cash on delivery", "recent_orders": "Recent Orders", "settings": "Settings", "profile_settings": "Profile Settings", "full_name": "Full name", "email": "Email", "phone": "Phone", "address": "Address", "app_settings": "App Settings", "languages": "Languages", "english": "English", "help_support": "Help & Support", "register": "Register", "lets_start_with_register": "Let's Start with register!", "should_be_more_than_3_letters": "Should be more than 3 letters", "john_doe": "<PERSON>", "should_be_a_valid_email": "Should be a valid email", "should_be_more_than_6_letters": "Should be more than 6 letters", "letsLoginToYourAccountFirst": "Let's Login to your\naccount first!", "password": "Password", "iHaveAnAccount": "I have an account ? ", "tracking_order": "Tracking Order", "discover__explorer": "Discover & Explorer", "reset_cart": "Reset Cart?", "cart": "<PERSON><PERSON>", "shopping_cart": "Shopping Cart", "verify_your_quantity_and_click_checkout": "Verify your quantity and click checkout", "lets_start_with_login": "Let's Start with Login!", "should_be_more_than_3_characters": "Should be more than 3 characters", "checkout_settings": "Checkout Settings", "checkout_type": "Checkout Type", "steps_checkout": "Steps Checkout", "one_page_checkout": "One Page Checkout", "show_checkout": "Show Checkout", "guest_checkout": "Guest Checkout", "email_field": "Email Field", "name_field": "Name Field", "phone_field": "Phone Field", "show_field": "Show Field", "required_field": "Required Field", "field_settings": "Field Settings", "you_must_add_products_of_the_same_markets_choose_one": "You must add products of the same markets choose one markets only!", "reset_your_cart_and_order_meals_form_this_market": "Reset your cart and order meals form this market", "keep_your_old_meals_of_this_market": "Keep your old meals of this market", "reset": "Reset", "close": "Close", "application_preferences": "Application Preferences", "help__support": "Help & Support", "light_mode": "Light Mode", "dark_mode": "Dark Mode", "log_out": "Log out", "version": "Version", "dont_have_any_item_in_your_cart": "D'ont have any item in your cart", "start_exploring": "Start Exploring", "dont_have_any_item_in_the_notification_list": "D'ont have any item in the notification list", "payment_settings": "Payment Settings", "not_a_valid_number": "Not a valid number", "not_a_valid_date": "Not a valid date", "not_a_valid_cvc": "Not a valid CVC", "cancel": "Cancel", "save": "Save", "edit": "Edit", "not_a_valid_full_name": "Not a valid full name", "email_address": "Email Address", "not_a_valid_email": "Not a valid email", "not_a_valid_phone": "Not a valid phone", "not_a_valid_address": "Not a valid address", "not_a_valid_biography": "Not a valid biography", "your_biography": "Your biography", "your_address": "Your Address", "search": "Search", "recents_search": "Recents Search", "verify_your_internet_connection": "Verify your internet connection", "carts_refreshed_successfuly": "Carts refreshed successfully", "the_product_was_removed_from_your_cart": "The $productName was removed from your cart", "category_refreshed_successfuly": "Category refreshed successfully", "notifications_refreshed_successfuly": "Notifications refreshed successfully", "order_refreshed_successfuly": "Order refreshed successfully", "orders_refreshed_successfuly": "Orders refreshed successfully", "market_refreshed_successfuly": "Market refreshed successfully", "profile_settings_updated_successfully": "Profile settings updated successfully", "payment_settings_updated_successfully": "Payment settings updated successfully", "tracking_refreshed_successfuly": "Tracking refreshed successfully", "welcome": "Welcome", "wrong_email_or_password": "Wrong email or password", "addresses_refreshed_successfuly": "Addresses refreshed successfuly", "delivery_addresses": "Delivery Addresses", "add": "Add", "new_address_added_successfully": "New Address added successfully", "the_address_updated_successfully": "The address updated successfully", "long_press_to_edit_item_swipe_item_to_delete_it": "Long press to edit item, swipe item to delete it", "home_address": "Home Address", "description": "Description", "hint_full_address": "12 Street, City 21663, Country", "full_address": "Full Address", "orders": "Orders", "history": "History", "delivered": "Delivered", "dismiss": "<PERSON><PERSON><PERSON>", "confirm": "Confirm", "confirmed": "Confirmed", "would_you_please_confirm_if_you_have_delivered_all_meals": "Would you please confirm if you have delivered all meals to client", "delivery_confirmation": "Delivery Confirmation", "products_ordered": "Products Ordered", "order_details": "Order Details", "address_not_provided_please_call_the_client": "Address not provided please call the client", "address_not_provided_contact_client": "Address not provided contact client", "orders_history": "Orders History", "email_to_reset_password": "Email to reset password", "send_password_reset_link": "Send link", "i_remember_my_password_return_to_login": "I remember my password return to login", "your_reset_link_has_been_sent_to_your_email": "Your reset link has been sent to your email", "error_verify_email_settings": "Error! Verify email settings", "order_satatus_changed": "Order status changed", "new_order_from_costumer": "New Order from costumer", "your_have_an_order_assigned_to_you": "Your have an order assigned to you", "unknown": "Unknown", "ordered_products": "Ordered Products", "noNotificationsFound": "No Notifications Found", "delivery_fee": "Shipping Cost", "items": "Items", "you_dont_have_any_order_assigned_to_you": "You don't have any order assigned to you!", "swip_left_the_notification_to_delete_or_read__unread": "Swipe left the notification to delete or read / unread it", "customer": "Customer", "quantity": "Quantity", "thisAccountNotExist": "This account not exist", "tapBackAgainToLeave": "Tap back again to leave", "phoneNumber": "Phone Number", "deliveryAddress": "Delivery Address", "fullName": "Full Name", "viewDetails": "View Details", "viewAttachment": "View Payment Attachment", "all": "All", "totalEarning": "Total Earnings", "totalOrders": "Total Orders", "totalMarkets": "Total Markets", "totalProducts": "Total Products", "myMarkets": "My Markets", "closed": "Closed", "open": "Open", "delivery": "Delivering", "pickup": "Pickup", "information": "Information", "featuredProducts": "Featured Products", "whatTheySay": "What They Say ?", "view": "View", "confirmation": "Confirmation", "yes": "Yes", "areYouSureYouWantToCancelThisOrderOf": "Are you sure you want to cancel this order of customer ?", "areYouSureYouWantToDeleteThisSize": "Are you sure you want to delete this size?", "areYouSureYouWantToDeleteYourAccount": "Are you sure you want to delete your account ?", "areYouSureYouWantToDeleteThisColor": "Are you sure you want to delete this color?", "editOrder": "Edit Order", "thisOrderUpdatedSuccessfully": "This order updated successfully", "assignDeliveryBoy": "Assign Del<PERSON><PERSON>", "hint": "Hint", "insertAnAdditionalInformationForThisOrder": "Insert an additional information for this order", "orderIdHasBeenCanceled": "Order: #{id} has been canceled", "canceled": "Canceled", "messages": "Messages", "youDontHaveAnyConversations": "You don't have any conversations", "newMessageFrom": "New message from", "typeToStartChat": "Type to start chat", "thisNotificationHasMarkedAsRead": "This notification has marked as read", "thisNotificationHasMarkedAsUnRead": "This notification has marked as un read", "notificationWasRemoved": "Notification was removed", "deliveryAddressRemovedSuccessfully": "Delivery Address removed successfully", "faqsRefreshedSuccessfully": "Faqs refreshed successfully", "favoritesRefreshedSuccessfully": "Favorites refreshed successfully", "thisProductWasAddedToFavorite": "This product was added to favorite", "thisProductWasRemovedFromFavorites": "This product was removed from favorites", "productRefreshedSuccessfully": "Product refreshed successfully", "youDontHaveMarketsPleaseSigninUsingAdminPanelAnd": "You don't have markets, please sign-in using admin panel and open new market", "goToHome": "Go To Home", "theConversationWithIsDismissed": "The conversation with #{name} is dismissed", "addProduct": "Add Product", "editProduct": "Edit Product", "addCategory": "Add category", "editCategory": "Edit category", "clickToBrowse": "Click to browse", "clickToBrowseLogo": "Click to browse Logo", "name": "Name", "businessName": "Business Name", "salePrice": "Sale Price", "Price": "Price", "onSale": "On Sale", "outOfStock": "Out Of Stock", "inStock": "In Stock", "orderDetails": "Order Details", "orderReceived": "Order Received", "preparing": "Preparing", "ready": "Ready", "addedSuccessfully": "Added successfully", "updatedSuccessfully": "Updated successfully", "deletedSuccessfully": "Deleted successfully", "somethingWentWrong": "Something went wrong", "noInternet": "No internet connection!", "cantConnectToServer": "Cannot connect to server. Please try again later...", "pleasePickImage": "Please pick an image", "requiredField": "Required field", "deleteCategoryConfirmationMessage": "Are you sure you want to delete this category?", "deleteCategoryConfirmationMessageYouWillDeleteMainCategoryAlsoBecauseItsLastSubCategory": "Are you sure you want to delete this category?\nYou will delete main category also because it's last sub category", "deleteProductConfirmationMessage": "Are you sure you want to delete this product?", "deleteProductConfirmationBanner": "Are you sure you want to delete this Banner?", "price": "Price", "appearance": "Appearance", "aboutUs": "About us", "termsAndCondition": "Terms & condition", "enter": "Enter", "completed": "Completed", "pending": "Pending", "refunded": "Refunded", "done": "Done", "taskDone": "Done", "failed": "Failed", "confirmTheChanges": "Would you please confirm if you want to save changes", "doNotHaveAnyOrder": "You don't have any orders 🙂", "products": "Products", "categories": "Categories", "delete": "Delete", "deleteAccount": "Delete Account", "orderStatus": "Order Status", "generalInformation": "General Information", "saveChanges": "Save Changes", "system": "System", "noUsersFound": "No Users Found", "customers": "Customers", "light": "Light", "state": "state", "city": "City", "streetName": "Street Name", "building": "Building", "floor": "Floor", "apartment": "Apartment", "dark": "Dark", "contactUs": "Contact us", "note": "Note", "maxUploadFilesIsOnly4": "Max upload images is only 4", "maxUploadFileSizeIsOnly10MB": "Max upload image size is only 10 MB", "facebook": "Facebook", "instagram": "Instagram", "tiktok": "Tiktok", "yourOrderStatusHasBeenChanged": "Your order status has been changed", "yourDeliveryCostHasBeenChanged": "Your delivery cost has been changed", "deliveryCostChangedTo": "Delivery cost changed to {cost}", "orderStatusChangedTo": "Order status changed to {status}", "noData": "No data", "noProductsInThisCategory": "No products in this category", "noCategories": "No categories", "colors": "Colors", "color": "Color", "sizes": "Sizes", "size": "Size", "sendNotification": "Send Notification", "send": "Send", "setting": "Settings", "addColors": "Add Colors", "addSizes": "Add Sizes", "accountSettings": "Account <PERSON><PERSON>", "editShipping": "Edit Shipping", "extraSettings": "Extra Settings", "select": "Select", "pleaseSelect": "Please select", "enterSize": "Enter size", "enterColor": "Enter color", "selectCategories": "Select categories", "editSize": "Edit size", "colorAlreadyExist": "Color already exist", "pickAColor": "Pick a color !", "noColorsAdded": "No Colors Added", "noSizesAdded": "No Sizes Added", "askMeAnything": "Ask me anything\n(Marketing, Sales, Tech, etc...)", "aiHintMessage": "Ask me anything...", "enterYourMessage": "Enter your message !", "categoryLimitReached": "Limit reached", "categoryLimitReachedDesc": "Upgrade your plan now and unlock unlimited addition, Enjoy full access without restrictions and grow your store seamlessly", "bannerLimitReached": "Banner limit reached !", "productLimitReached": "Products limit reached !\nPlease upgrade your plan...", "yourSubscriptionHasExpired": "Your subscription has expired", "pleaseContactSupport": "Please contact support", "dontHaveAccount": "Don't have an account?", "phoneIsInvalid": "Phone is invalid", "pleaseSelectSubCategories": "Please select Sub Categories", "emailIsInvalid": "<PERSON><PERSON> is invalid", "date": "Date", "orderTotal": "Order Total", "emailAlreadyExist": "Email already exist", "noProductsFound": "No products found", "store": "Store", "invoices": "Invoices", "checkedOutSuccessfully": "Checked Out Successfully", "sendSuccessfully": "Sent Successfully", "addToCart": "Add To Cart", "product": "Product", "customerInfo": "Customer Information", "subscribeNow": "Subscribe Now", "subscribe": "Subscribe", "users": "Users", "dateAndTime": "Date & Time", "searchProduct": "Search Product", "tasks": "Tasks", "expenses": "Expenses", "addExpenses": "Add Expenses", "addTask": "Add Task", "editTask": "Edit Task", "isPaid": "<PERSON>", "isDone": "Is Done", "paid": "Paid", "unpaid": "UnPaid", "deleteTask": "Delete Task", "deleteExpense": "Delete Expense", "totalTasks": "Total Tasks", "uncompleted": "Uncompleted", "subCategories": "Sub Categories", "showPricing": "Show Pricing", "areYouSureYouWantToDeleteThisTask": "Are You Sure You Want To Delete This Task ?", "areYouSureYouWantToDeleteThisExpense": "Are You Sure You Want To Delete This Expense ?", "areYouSureYouWantToDeleteThisPayment": "Are You Sure You Want To Delete This Payment Method ?", "noDataFound": "No Data Found", "editedSuccessfully": "Edited Successfully", "yourSettings": "Your Settings", "salePriceMustBeLessThanPrice": "Sale Price must be less than Price", "sizesAndColors": "Sizes & Colors", "banners": "Banners", "noBanners": "No Banners", "noExpenses": "No Expenses", "noTasks": "No Tasks", "addBanner": "Add Banner", "editBanner": "Edit Banner", "bestImageSize": "3000W X 1200H is a great size for Banner", "appSettings": "App Settings", "optional": "Optional", "title": "Title", "inventory": "Inventory", "website": "Website", "paymentMethods": "Payment Methods", "myWebsite": "My Website", "choosePaymentMethods": "Choose Payment Methods", "addPaymentMethod": "Add Payment Method", "editPaymentMethod": "Edit Payment Method", "bankAccount": "Bank Account", "Or": "Or", "webLinkMessage": "Welcome To IDEA2APP - You can see your website from this link below.\nWe refer to adding your products first to see your website in the best way.", "shippingCost": "Shipping Cost", "copiedToClipboard": "Copied to clipboard", "featured": "Featured", "addStocks": "Add Stock", "area": "Area", "youMustTurnOffInventoryFirst": "You must turn off inventory first", "youMustTurnOnInventoryFirst": "You must turn on inventory first", "on": "On", "off": "Off", "phone2": "Phone 2", "colorsAndSizesStock": "Colors & Sizes Stock", "ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock": "If you turn on the inventory you will remove size & colors stock", "ifYouTurnOofTheInventoryYouWillRemoveTheProductStock": "If you turn off the inventory you will remove the product stock", "cantChangeStatus": "You can't change the status", "ifYouDontNeedToManageProductStockMakeSureToTurnOnTheInventoryAndLeaveItEmpty": "If you don't need to manage product stock make sure to turn on the inventory and leave it empty", "addColorsStock": "Add Colors Stock", "addSizesStock": "Add Sizes Stock", "youMust": "You must ", "ContactSupport": "Contact Support ", "ToActivateYourOnlinePayment": "To activate your online payment", "senderInfo": "Sender Information", "waitingPayment": "Waiting Payment...", "noPaymentMethods": "No Payment Methods", "businessType": "Business Type", "template": "Template", "theTemplate": "Template", "selectYourBusinessType": "Select your business type", "orderId": "Order Id", "userName": "User Name", "userPhone": "User Phone", "invoiceId": "Invoice Id", "searchBy": "Search By", "filterByDate": "Filter By Date", "clearFilter": "Clear Filter", "reports": "Reports", "today": "Today", "lastWeek": "Last Week", "lastMonth": "Last Month", "lastYear": "Last Year", "from": "From", "to": "To", "noOrdersFound": "No Orders Found", "lastQuarter": "Last Quarter", "lastSemiAnnual": "Last Semi Annual", "noDataAvailable": "No Data Available", "noStoreInvoices": "No Store Invoices", "storeInvoices": "Store Invoices", "plan": "Plan", "free": "Free", "monthly": "Monthly", "threeMonths": "3 Months", "sixMonths": "6 Months", "annually": "Annually", "deleted": "Deleted", "myWebsiteLink": "My Website Link", "expired": "Expired", "active": "Active", "days": "Days", "ago": "ago", "renewNow": "Renew Now", "left": "left", "earnings": "Earnings", "stepsToCreateYourFreeWebSite": "Steps to create your free website", "addYourFirstCategory": "Add your first category", "addYourFirstProduct": "Add your first product", "addYourFirstBanner": "Add your first banner", "pleaseAddCategoryFirst": "Please add category first", "apply": "Apply", "filterWithDateRange": "Filter With Date Range", "itsNotActiveAndWillNotBeDisplayedToYourCustomers": "It's not active and will not be displayed to your customers", "websiteName": "Website Name", "enterWebsiteName": "Enter Website Name", "invalidWebsiteName": "Invalid Website Name", "freeWebsite": "Free Website", "premiumWebsite": "Premium Website", "noDomainFees": "(No domain fees)", "ifYouNeedToChangeYourWebsiteNamePlease": "If you need to change your website name please", "websiteNameAlreadyExist": "Website Name already exist", "emailAndWebsiteNameAlreadyExist": "Email & Website Name already exist", "vendors": "Vend<PERSON>", "addNewVendor": "Add New Vendor", "editVendor": "<PERSON>", "isActive": "Is Active", "expireDate": "Expire Date", "vendorDeletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "noVendorsFound": "No Vendors Found", "vendorStatus": "Vendor Status", "startDate": "Start Date", "paidAmount": "<PERSON><PERSON>", "subscriptionType": "Subscription Type", "pricingPlan": "Pricing Plan", "remainingAmount": "Remaining Amount", "areYouSureYouWantToDeleteThisVendor": "Are you sure you want to delete this vendor?", "stepsToCreateYourWebSite": "Steps to create your website", "oneMonth": "One Month", "oneTimePrice": "One Time Price", "chooseYourPlan": "Choose Your Plan", "payNow": "Pay Now", "paymentAttachment": "Payment Attachment", "payWithInstapay": "Pay with Instapay", "payWithVodafoneCash": "Pay with Vodafone Cash", "subscriptionPlan": "Subscription Plan", "paymentSentSuccessfully": "Payment sent successfully", "yourRequestSentSuccessfully": "Your request has been successfully sent and is currently under review. We will provide a response as soon as possible.", "sendRequest": "Send Request", "pleaseAttachPaymentAttachment": "Please attach payment attachment", "socialLinks": "Social Links & About", "aboutAndPrivacy": "About & Privacy", "YourFacebookLink": "Your Facebook Link", "YourInstagramLink": "Your Instagram Link", "YourTiktokLink": "Your Tiktok Link", "YourWhatsApp": "Your WhatsApp Number", "socialLinksHint": "If you fill in any of these fields, they will be visible to your clients. If you leave any of them empty, those specific links will not be shown.", "enterValidLink": "Enter a valid link", "privacyPolicy": "Privacy Policy", "passwordLength": "Password length should be more than 8 characters", "enterValidNumber": "Enter a valid number", "enterValidEmail": "Enter a valid email", "enterValidPhoneNumber": "Enter a valid phone number", "subscriptionRequests": "Subscription Requests", "paymentMethod": "Payment Method", "requestDate": "Request Date", "areYouSureYouWantToDeleteThisSubscriptionRequest": "Are you sure you want to delete this subscription request?", "approved": "Approved", "approveSubscriptionRequest": "Approve Subscription Request", "areYouSureYouWantToApproveThisSubscriptionRequest": "Are you sure you want to approve this subscription request?", "yourAccountIsNotActive": "Your account is not active", "yourAccountIsNotActivePleaseRenewYourSubscription": "Your account is not active, please renew your subscription !", "approveRequest": "Approve Request", "approve": "Approve", "requestApproved": "Request Approved", "subscriptionRequestApprovedSuccessfully": "Subscription request approved successfully", "changePlan": "Change Plan", "phoneOrPayLink": "phone number or pay link", "noResultFound": "No result found", "selectVendor": "Select Vendor", "noSubscriptionRequests": "No Subscription Requests", "discount": "Discount", "code": "Code", "isPercentage": "Is Percentage", "showInList": "Show In List", "addPromoCode": "Add Promo Code", "editPromoCode": "Edit Promo Code", "deletePromoCode": "Delete Promo Code", "deletePromoCodeConfirmationMessage": "Are you sure you want to delete this promo code?", "promoCodes": "Promo Codes", "noPromoCodes": "No Promo Codes", "ifYouEnableThisCustomersWillSeeThePromoCodeInTheAppOrWebsitePromoList": "If you enable this, customers will see the promo code in the app or website promo list", "promoCodeAlreadyExist": "Promo code already exist", "areYouSureYouWantToDeleteThisPromoCode": "Are you sure you want to delete this promo code?", "youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice": "You can't change the price of the product because it is set by the size price !", "pleaseAddPriceToYourAllSizes": "Please add price to your all sizes", "promoCode": "Promo Code", "storeActive": "Store Active", "minimumOrderCost": "Minimum Order Cost", "back": "Back", "newOrdersAlertBell": "New Orders Al<PERSON>", "freeShipping": "Free Shipping", "newOrder": "new order", "youHaveNewOrder": "You have new order", "youHaveNewOrderPleaseCheckYourLatestOrders": "You have new order, please check your latest orders !", "exitTheApp": "Exit the app", "areYouSureYouWantToExitTheApp": "Are you sure you want to exit the app?", "youMayNeedToReInstallTheAppToApplyChanges": "You may need to re-install the app to apply changes", "arabic": "Arabic", "youMustAddOneOfThoseNames": "You must add one of those names", "englishName": "English Name", "arabicName": "Arabic Name", "englishDescription": "English Description", "arabicDescription": "Arabic Description", "youMustAddOneOfThoseDescriptions": "You must add at least one description in Arabic or English.", "youMustAddOneOfThoseTitles": "You must add at least one title in Arabic or English.", "sizeAlreadyExist": "Size already exist", "youMustAddAtLeastOneName": "You must add at least one name in Arabic or English.", "qrLanding": "QR Landing", "qrType": "QR Type", "generate": "Generate", "chooseQRType": "Choose QR Type", "pleaseChooseQrType": "Please choose QR Type", "qr": "QR", "share": "Share", "visitWebsite": "Visit Website", "qrSettings": "QR Settings", "cartIsEmpty": "Cart is empty", "showLogo": "Show My Logo", "logoSize": "Logo Size", "large": "Large", "medium": "Medium", "small": "Small", "YourYoutubeLink": "Your Youtube Link", "playStore": "Play Store", "appStore": "App Store", "productStock": "Product Stock", "colorStockIInventoryIsOff": "Color Stock is off", "sizeStockIInventoryIsOff": "Size Stock is off", "manageSizesAndColors": "Manage Sizes & Colors", "stock": "Stock", "productStatus": "Product Status", "primaryColor": "Primary Color", "applicationAndWebsiteSettings": "Application / Website Settings", "ordersSettings": "Orders Settings", "themeAndLanguageSettings": "Theme & Language Settings", "defaultLanguage": "Default Language", "defaultTheme": "Default Theme", "defaultThemeDescription": "This theme will be the default for your clients in the app or website.", "link": "Link", "qrSquare": "QR Square", "qrCircle": "QR Circle", "dashboard": "Dashboard", "mainCategories": "Main Categories", "addMainCategory": "Add Main Category", "editMainCategory": "Edit Main Category", "defaultLanguageDescription": "This language will be the default for your clients in the app or website.", "youMustAddStockForColorOrSizes": "you must add stock for the\ncolors or sizes", "sortProducts": "Sort Products", "sortProductsDescription": "Drag and drop products to change their order. Arrange them the way you prefer.", "bulkEdit": "Edit Prices", "bulkEditProducts": "edit products prices", "selectProducts": "Select Products", "selectedProducts": "Selected Products", "updateSelected": "Update Selected", "sortMainCategories": "Sort Main Categories", "sortMainCategoriesDescription": "Drag and drop main categories to change their order. Arrange them the way you prefer.", "categoriesTab": "Categories", "mainCategoriesTab": "Main Categories", "more": "more", "noChangesToSave": "No changes to save", "bulkEditSavedSuccessfully": "<PERSON><PERSON><PERSON> edit saved successfully", "sizePricing": "Size Pricing", "salePriceShouldBeLessThanPrice": "Sale price should be less than regular price", "IfYouDontWantToAddStockForThemYouCanChooseProductStockOrTurnOffTheInventory": "If you don't want to add stock for them, you can choose product stock or turn off the inventory", "noSubCategories": "No sub-categories available", "noSelectedSubCategories": "No Sub Categories", "areasIn": "Areas In {cityName}", "noAreasFound": "No Areas Found", "@noAreasFound": {"description": "Message shown when no areas are found in search"}, "areas": "Areas", "sortCategories": "Sort Categories", "sortCategoriesDescription": "Hold and drag a category to change its position. Arrange them the way you prefer.", "cost": "Cost", "addArea": "Add Area", "suggestions": "Suggestions", "suggestionsAreas": "Suggestions Areas", "selectAll": "Select All", "selectAreas": "Select Areas", "selectedAreas": "Selected Areas", "youMustButCostForAllAreasYouSelected": "You must add cost for all areas you selected", "shareLink": "Share Link", "shareQr": "Share QR", "latestOrders": "Latest Orders", "freePlan": "Free Plan", "deleteCategory": "Delete Category", "aiAssistant": "AI Assistant", "categoriesSettings": "Categories Settings", "mainCategory": "Main Category", "mainCategorySettingsDesc": "Create a main category to group related subcategories under one parent. For example: a 'Men' category can include 'Jackets', 'T-Shirts', and 'Pants'.", "categoriesWithoutMainCategory": "Categories Without Main Category", "areYouSureYouWantToDeleteThisMainCategory": "Are you sure you want to delete this main category {mainCategoryName} ?", "totalProfit": "Total\nEarnings", "dailyOrders": "Daily\nOrders", "dailyProfit": "Daily\nEarnings", "totalOrdersHome": "Total\nOrders", "appAndWebsiteSettings": "App & Website Settings", "ordersAndShipping": "Orders & Shipping", "storeAppearance": "Store Appearance", "paymentsAndPromotions": "Payments & Promotions", "reportsAndOperations": "Reports & Operations", "showDetails": "Show Details", "hide": "<PERSON>de", "usersOrders": "Users Orders", "deleteSale": "Delete Sale", "noUsersOrders": "No Users Orders", "failedToSendNotificationsToUsers": "Failed to send notifications to users", "PayAttachment": "Pay Attachment", "invoice": "Invoice", "attachment": "Attachment", "thisProductIsDeleted": "This product is deleted", "unlimited": "Unlimited", "chooseCategoriesTypeYouWantToSort": "Choose Categories Type\nYou Want To Sort", "areYouSureYouWantToDeleteThisArea": "Are you sure you want to delete this area?", "changeWebsiteLater": "(Note: You can change to a custom domain anytime)", "updateAvailable": "Update Available", "updateAvailableDesc": "A new version of the app is available. Please update the app to continue using it.", "updateNow": "Update Now", "globalOffer": "Global Sale", "sale": "Sale", "saleQuantityPrice": "Sale Quantity Price", "saleQuantityPriceDesc": "Set how many items the customer must buy to get the sale price", "globalOfferDec": "It is a general Sale that is applied to all products in the store", "globalOfferDecRimender": "You’ve set a {discount} global discount for all your products.", "globalDiscountError": "Your global discount made some prices negative. Please adjust it.", "editGlobalOfferError": "The Percentage should be less than 100%", "pleaseAddSizes": "Please Add Sizes", "deleteOffer": "Delete Offer", "youMustAddMinQuantitySaleNumberAndQuantitySale": "You must add min quantity sale number and quantity sale", "selectCurrency": "Select Currency", "selectYourCurrencyForTheAppsYouCanChangedLatter": "Select Your Currency For The Apps\nYou Can Change It Latter", "selectCountry": "Select Country", "selectYourCountryForTheAppsYouCanChangedLatter": "Select Your Country For The Apps\nYou Can Change It Latter", "addAndManageEmployees": "Add & Manage Employees", "inviteEmployeesToYourStore": "Invite employees to your store and set\ntheir access permissions easily", "addEmployee": "Add Employee", "permissions": "Permissions", "next": "Next", "viewOrders": "View Orders", "manageOrders": "Manage Orders", "viewCategoriesProducts": "View Categories & Products", "manageCategoriesProducts": "Manage Categories & Products", "createStoreOrders": "Create Store Orders", "manageStoreOrders": "Manage Store Orders", "accessAppSettings": "Access App & Website Settings", "accessOrdersShipping": "Access Orders & Shipping", "accessStoreAppearance": "Access Store Appearance", "accessPaymentsPromotions": "Access Payments & Promotions", "accessUsers": "Access Users", "accessReportsOperations": "Access Reports & Operations", "allRoles": "All Roles", "productsAndCategories": "Products & Categories", "yourWebsiteName": "your-website-name", "autoGeneratedFromName": "Auto-generated from name", "searchEmployees": "Search Employees", "searchByNameOrEmail": "Search by name or email", "deleteEmployee": "Delete Employee", "areYouSureDeleteEmployee": "Are you sure you want to delete this employee?", "noName": "No Name", "noEmail": "No Email", "inactive": "Inactive", "roles": "roles", "pleaseSelectAtLeastOneRole": "Please select at least one role", "editBasicInfo": "Edit Basic Info", "editPermissions": "Edit Permissions", "editEmployee": "Edit Employee", "status": "Status", "manageEmployees": "Manage Employees", "noAccessTo": "No Access to", "contactAdminForAccess": "Contact your administrator for access", "upgradePlan": "Upgrade Plan", "contactSupport": "Contact Support", "dashboardTutorial": "Dashboard Tutorial", "customized_description": "Customized Description", "rich_text_editor": "Rich Text Editor", "preview": "Preview", "quick_templates": "Quick Templates", "vegetables_list": "Vegetables List", "fruits_list": "Fruits List", "product_features": "Product Features", "general_settings": "General Settings", "enter_your_content_here": "Enter your content here...", "insert_template": "Insert Template"}